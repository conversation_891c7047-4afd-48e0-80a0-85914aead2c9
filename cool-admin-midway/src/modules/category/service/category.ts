import { Provide } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { CategoryEntity } from '../entity/category';
import { Repository } from 'typeorm';

/**
 * 分类服务
 */
@Provide()
export class CategoryService extends BaseService {
  @InjectEntityModel(CategoryEntity)
  categoryEntity: Repository<CategoryEntity>;

  /**
   * 新增分类
   */
  async add(param: any) {
    const { parentId } = param;

    // 设置层级
    if (parentId) {
      const parent = await this.categoryEntity.findOne({
        where: { id: parentId },
      });
      if (parent) {
        param.level = parent.level + 1;
        // 生成路径层级ID
        const parentPathIds = parent.pathIds
          ? parent.pathIds
          : parent.id.toString();
        param.pathIds = `${parentPathIds},${param.id}`;
      }
    } else {
      param.level = 1;
    }

    return super.add(param);
  }

  /**
   * 修改分类
   */
  async update(param: any) {
    const { id, parentId } = param;
    const exists = await this.categoryEntity.findOne({
      where: { id },
    });

    if (exists) {
      // 如果修改了父级ID，需要更新层级和路径
      if (parentId !== exists.parentId) {
        // 设置层级
        if (parentId) {
          const parent = await this.categoryEntity.findOne({
            where: { id: parentId },
          });
          if (parent) {
            param.level = parent.level + 1;
            // 生成路径层级ID
            const parentPathIds = parent.pathIds
              ? parent.pathIds
              : parent.id.toString();
            param.pathIds = `${parentPathIds},${id}`;
          }
        } else {
          param.level = 1;
          param.pathIds = id.toString();
        }

        // 更新所有子分类的层级和路径
        await this.updateChildren(id, param.level, param.pathIds);
      }
    }

    return super.update(param);
  }

  /**
   * 更新子分类信息
   */
  private async updateChildren(
    parentId: number,
    parentLevel: number,
    parentPathIds: string
  ) {
    // 查找所有子分类
    const children = await this.categoryEntity.find({
      where: { parentId },
    });

    for (const child of children) {
      // 更新层级
      const level = parentLevel + 1;
      const pathIds = `${parentPathIds},${child.id}`;

      await this.categoryEntity.update(child.id, {
        level,
        pathIds,
      });

      // 递归更新子级的子级
      await this.updateChildren(child.id, level, pathIds);
    }
  }

  /**
   * 删除分类
   */
  async delete(ids: number[]) {
    // 检查是否有子分类
    for (const id of ids) {
      const count = await this.categoryEntity.count({
        where: { parentId: id },
      });

      if (count > 0) {
        throw new Error('该分类下有子分类，无法删除');
      }
    }

    return super.delete(ids);
  }

  /**
   * 获取分类树形结构
   * @param regionId 可选地区ID
   */
  async tree(regionId?: number) {
    const query: any = {};
    if (regionId) {
      query.regionId = regionId;
    }

    // 获取所有分类
    const categories = await this.categoryEntity.find({
      where: query,
      order: {
        orderNum: 'ASC', // 按排序号升序
      },
    });

    // 构建树形结构
    const buildTree = (parentId: number | null = null) => {
      return categories
        .filter(item => item.parentId === parentId)
        .map(item => {
          const children = buildTree(item.id);
          return {
            ...item,
            children: children.length > 0 ? children : undefined,
          };
        });
    };

    return buildTree(null);
  }

  /**
   * 分类移动操作
   * @param id 分类ID
   * @param action 移动方向 up|down|top|bottom
   */
  async move(id: number, action: 'up' | 'down' | 'top' | 'bottom') {
    // 查询当前分类信息
    const curr = await this.categoryEntity.findOne({
      where: { id },
    });

    if (!curr) {
      throw new Error('分类不存在');
    }

    // 获取同级分类，按排序号排序
    const siblings = await this.categoryEntity.find({
      where: { parentId: curr.parentId },
      order: {
        orderNum: 'ASC',
      },
    });

    // 当前分类的索引
    const index = siblings.findIndex(item => item.id === id);

    switch (action) {
      case 'up':
        if (index <= 0) return false; // 已经是第一个
        await this.swapOrder(curr, siblings[index - 1]);
        break;
      case 'down':
        if (index >= siblings.length - 1) return false; // 已经是最后一个
        await this.swapOrder(curr, siblings[index + 1]);
        break;
      case 'top':
        if (index === 0) return false; // 已经是第一个
        await this.moveToEdge(curr, siblings, true);
        break;
      case 'bottom':
        if (index === siblings.length - 1) return false; // 已经是最后一个
        await this.moveToEdge(curr, siblings, false);
        break;
    }

    return true;
  }

  /**
   * 交换两个分类的排序号
   */
  private async swapOrder(a: CategoryEntity, b: CategoryEntity) {
    const temp = a.orderNum;
    await this.categoryEntity.update(a.id, { orderNum: b.orderNum });
    await this.categoryEntity.update(b.id, { orderNum: temp });
  }

  /**
   * 将分类移动到顶部或底部
   */
  private async moveToEdge(
    curr: CategoryEntity,
    siblings: CategoryEntity[],
    isTop: boolean
  ) {
    if (isTop) {
      // 移到顶部，设置比第一个还小的排序号
      const firstOrderNum = siblings[0].orderNum;
      await this.categoryEntity.update(curr.id, {
        orderNum: firstOrderNum - 10,
      });
    } else {
      // 移到底部，设置比最后一个还大的排序号
      const lastOrderNum = siblings[siblings.length - 1].orderNum;
      await this.categoryEntity.update(curr.id, {
        orderNum: lastOrderNum + 10,
      });
    }
  }

  /**
   * 修复regionId为null的分类数据
   * @param defaultRegionId 默认地区ID
   */
  async fixNullRegionCategories(defaultRegionId: number) {
    // 查找所有regionId为null的分类
    const nullRegionCategories = await this.categoryEntity.find({
      where: { regionId: null },
    });

    if (nullRegionCategories.length > 0) {
      console.log(`发现 ${nullRegionCategories.length} 个regionId为null的分类，将关联到地区ID: ${defaultRegionId}`);

      // 批量更新
      for (const category of nullRegionCategories) {
        await this.categoryEntity.update(category.id, {
          regionId: defaultRegionId,
        });
      }

      console.log('修复完成');
      return nullRegionCategories.length;
    }

    return 0;
  }

  /**
   * 获取regionId为null的分类列表
   */
  async getNullRegionCategories() {
    return await this.categoryEntity.find({
      where: { regionId: null },
    });
  }

  /**
   * 复制分类结构到其他地区
   * @param sourceRegionId 源地区ID
   * @param targetRegionId 目标地区ID
   * @param options 复制选项
   */
  async copyCategories(
    sourceRegionId: number,
    targetRegionId: number,
    options: {
      overwrite?: boolean;
      keepOrder?: boolean;
      copyStatus?: boolean;
      copyIcon?: boolean;
      copyDescription?: boolean;
    } = {}
  ) {
    // 获取源地区的所有分类
    const sourceCategories = await this.categoryEntity.find({
      where: { regionId: sourceRegionId },
      order: { orderNum: 'ASC', id: 'ASC' },
    });

    if (sourceCategories.length === 0) {
      throw new Error('源地区没有分类数据');
    }

    // 检查目标地区是否已有分类
    const existingCategories = await this.categoryEntity.find({
      where: { regionId: targetRegionId },
    });

    if (existingCategories.length > 0 && !options.overwrite) {
      throw new Error('目标地区已有分类数据，请选择覆盖选项');
    }

    // 如果选择覆盖，先删除目标地区的所有分类
    if (options.overwrite && existingCategories.length > 0) {
      await this.categoryEntity.delete({ regionId: targetRegionId });
    }

    // 创建ID映射表，用于处理父子关系
    const idMapping = new Map<number, number>();
    const createdCategories: CategoryEntity[] = [];

    // 按层级排序，确保父级先创建
    const sortedCategories = sourceCategories.sort((a, b) => {
      if (a.level !== b.level) {
        return a.level - b.level;
      }
      return a.orderNum - b.orderNum;
    });

    // 复制分类
    for (const sourceCategory of sortedCategories) {
      const newCategory = new CategoryEntity();

      // 基础字段
      newCategory.name = sourceCategory.name;
      newCategory.regionId = targetRegionId;
      newCategory.level = sourceCategory.level;

      // 可选字段
      if (options.keepOrder) {
        newCategory.orderNum = sourceCategory.orderNum;
      } else {
        newCategory.orderNum = createdCategories.length + 1;
      }

      if (options.copyStatus) {
        newCategory.status = sourceCategory.status;
      } else {
        newCategory.status = 1; // 默认启用
      }

      if (options.copyIcon) {
        newCategory.icon = sourceCategory.icon;
      }

      if (options.copyDescription) {
        newCategory.description = sourceCategory.description;
      }

      // 处理父子关系
      if (sourceCategory.parentId) {
        const newParentId = idMapping.get(sourceCategory.parentId);
        if (newParentId) {
          newCategory.parentId = newParentId;
        } else {
          console.warn(`找不到父级分类映射: ${sourceCategory.parentId}`);
          newCategory.parentId = null;
          newCategory.level = 1; // 如果找不到父级，设为顶级
        }
      } else {
        newCategory.parentId = null;
      }

      // 保存新分类
      const savedCategory = await this.categoryEntity.save(newCategory);
      createdCategories.push(savedCategory);

      // 记录ID映射
      idMapping.set(sourceCategory.id, savedCategory.id);
    }

    return {
      sourceRegionId,
      targetRegionId,
      copiedCount: createdCategories.length,
      categories: createdCategories,
    };
  }

  /**
   * 批量复制分类到多个地区
   * @param sourceRegionId 源地区ID
   * @param targetRegionIds 目标地区ID数组
   * @param options 复制选项
   */
  async batchCopyCategories(
    sourceRegionId: number,
    targetRegionIds: number[],
    options: any = {}
  ) {
    const results = [];
    const errors = [];

    for (const targetRegionId of targetRegionIds) {
      try {
        const result = await this.copyCategories(sourceRegionId, targetRegionId, options);
        results.push(result);
      } catch (error) {
        errors.push({
          targetRegionId,
          error: error.message,
        });
      }
    }

    return {
      success: results,
      errors,
      totalTargets: targetRegionIds.length,
      successCount: results.length,
      errorCount: errors.length,
    };
  }


}
