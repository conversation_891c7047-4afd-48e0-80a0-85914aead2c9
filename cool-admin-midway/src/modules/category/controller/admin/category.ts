import { Inject, Controller, Get, Param, Post, Body } from '@midwayjs/core';
import { CoolController, BaseController } from '@cool-midway/core';
import { CategoryEntity } from '../../entity/category';
import { CategoryService } from '../../service/category';

/**
 * 分类管理
 */
@Controller('/admin/category')
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: CategoryEntity,
  service: CategoryService,
  listQueryOp: {
    fieldEq: ['regionId', 'parentId', 'status'], // 支持按regionId、parentId、status精确查询
    keyWordLikeFields: ['name'], // 支持按name模糊查询
    addOrderBy: {
      orderNum: 'ASC', // 按排序号升序
    },
  },
  pageQueryOp: {
    fieldEq: ['regionId', 'parentId', 'status'], // 支持按regionId、parentId、status精确查询
    keyWordLikeFields: ['name'], // 支持按name模糊查询
    addOrderBy: {
      orderNum: 'ASC', // 按排序号升序
    },
  },
})
export class CategoryController extends BaseController {
  @Inject()
  categoryService: CategoryService;

  /**
   * 获取分类树形结构
   */
  @Get('/tree')
  async tree() {
    return this.ok(await this.categoryService.tree());
  }

  /**
   * 获取指定地区的分类树形结构
   */
  @Get('/tree/:regionId')
  async treeByRegion(@Param('regionId') regionId: number) {
    return this.ok(await this.categoryService.tree(regionId));
  }

  /**
   * 分类移动操作
   * @param id 分类ID
   * @param action 移动方向 up|down|top|bottom
   */
  @Post('/move')
  async move(
    @Body() body: { id: number; action: 'up' | 'down' | 'top' | 'bottom' }
  ) {
    const { id, action } = body;
    return this.ok(await this.categoryService.move(id, action));
  }

  /**
   * 修复regionId为null的分类数据
   */
  @Post('/fixNullRegion')
  async fixNullRegion(@Body() body: { defaultRegionId: number }) {
    const { defaultRegionId } = body;
    const count = await this.categoryService.fixNullRegionCategories(defaultRegionId);
    return this.ok({ fixedCount: count, message: `已修复 ${count} 个分类的地区关联` });
  }

  /**
   * 获取regionId为null的分类列表
   */
  @Get('/nullRegionCategories')
  async getNullRegionCategories() {
    return this.ok(await this.categoryService.getNullRegionCategories());
  }

  /**
   * 复制分类结构到其他地区
   */
  @Post('/copyCategories')
  async copyCategories(@Body() body: {
    sourceRegionId: number;
    targetRegionId: number;
    options?: {
      overwrite?: boolean;
      keepOrder?: boolean;
      copyStatus?: boolean;
      copyIcon?: boolean;
      copyDescription?: boolean;
    };
  }) {
    const { sourceRegionId, targetRegionId, options = {} } = body;
    const result = await this.categoryService.copyCategories(sourceRegionId, targetRegionId, options);
    return this.ok(result);
  }

  /**
   * 批量复制分类到多个地区
   */
  @Post('/batchCopyCategories')
  async batchCopyCategories(@Body() body: {
    sourceRegionId: number;
    targetRegionIds: number[];
    options?: any;
  }) {
    const { sourceRegionId, targetRegionIds, options = {} } = body;
    const result = await this.categoryService.batchCopyCategories(sourceRegionId, targetRegionIds, options);
    return this.ok(result);
  }


}
