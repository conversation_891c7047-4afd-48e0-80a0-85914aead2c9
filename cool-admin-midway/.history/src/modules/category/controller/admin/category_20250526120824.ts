import { Inject, Controller, Get, Param, Post, Body } from '@midwayjs/core';
import { CoolController, BaseController } from '@cool-midway/core';
import { CategoryEntity } from '../../entity/category';
import { CategoryService } from '../../service/category';

/**
 * 分类管理
 */
@Controller('/admin/category')
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: CategoryEntity,
  service: CategoryService,
})
export class CategoryController extends BaseController {
  @Inject()
  categoryService: CategoryService;

  /**
   * 获取分类树形结构
   */
  @Get('/tree')
  async tree() {
    return this.ok(await this.categoryService.tree());
  }

  /**
   * 获取指定地区的分类树形结构
   */
  @Get('/tree/:regionId')
  async treeByRegion(@Param('regionId') regionId: number) {
    return this.ok(await this.categoryService.tree(regionId));
  }

  /**
   * 分类移动操作
   * @param id 分类ID
   * @param action 移动方向 up|down|top|bottom
   */
  @Post('/move')
  async move(
    @Body() body: { id: number; action: 'up' | 'down' | 'top' | 'bottom' }
  ) {
    const { id, action } = body;
    return this.ok(await this.categoryService.move(id, action));
  }
}
