import { Inject, Controller, Get, Param } from '@midwayjs/core';
import { BaseController } from '@cool-midway/core';
import { CategoryService } from '../../service/category';

/**
 * 分类查询接口
 */
@Controller('/app/category')
export class AppCategoryController extends BaseController {
  @Inject()
  categoryService: CategoryService;

  /**
   * 获取分类树形结构
   */
  @Get('/tree')
  async tree() {
    return this.ok(await this.categoryService.tree());
  }

  /**
   * 获取指定地区的分类树形结构
   */
  @Get('/tree/:regionId')
  async treeByRegion(@Param('regionId') regionId: number) {
    return this.ok(await this.categoryService.tree(regionId));
  }
}
