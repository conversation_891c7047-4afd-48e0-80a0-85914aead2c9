import { Provide } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { CategoryEntity } from '../entity/category';
import { Repository } from 'typeorm';

/**
 * 分类服务
 */
@Provide()
export class CategoryService extends BaseService {
  @InjectEntityModel(CategoryEntity)
  categoryEntity: Repository<CategoryEntity>;

  /**
   * 新增分类
   */
  async add(param: any) {
    const { parentId } = param;

    // 设置层级
    if (parentId) {
      const parent = await this.categoryEntity.findOne({
        where: { id: parentId },
      });
      if (parent) {
        param.level = parent.level + 1;
        // 生成路径层级ID
        const parentPathIds = parent.pathIds
          ? parent.pathIds
          : parent.id.toString();
        param.pathIds = `${parentPathIds},${param.id}`;
      }
    } else {
      param.level = 1;
    }

    return super.add(param);
  }

  /**
   * 修改分类
   */
  async update(param: any) {
    const { id, parentId } = param;
    const exists = await this.categoryEntity.findOne({
      where: { id },
    });

    if (exists) {
      // 如果修改了父级ID，需要更新层级和路径
      if (parentId !== exists.parentId) {
        // 设置层级
        if (parentId) {
          const parent = await this.categoryEntity.findOne({
            where: { id: parentId },
          });
          if (parent) {
            param.level = parent.level + 1;
            // 生成路径层级ID
            const parentPathIds = parent.pathIds
              ? parent.pathIds
              : parent.id.toString();
            param.pathIds = `${parentPathIds},${id}`;
          }
        } else {
          param.level = 1;
          param.pathIds = id.toString();
        }

        // 更新所有子分类的层级和路径
        await this.updateChildren(id, param.level, param.pathIds);
      }
    }

    return super.update(param);
  }

  /**
   * 更新子分类信息
   */
  private async updateChildren(
    parentId: number,
    parentLevel: number,
    parentPathIds: string
  ) {
    // 查找所有子分类
    const children = await this.categoryEntity.find({
      where: { parentId },
    });

    for (const child of children) {
      // 更新层级
      const level = parentLevel + 1;
      const pathIds = `${parentPathIds},${child.id}`;

      await this.categoryEntity.update(child.id, {
        level,
        pathIds,
      });

      // 递归更新子级的子级
      await this.updateChildren(child.id, level, pathIds);
    }
  }

  /**
   * 删除分类
   */
  async delete(ids: number[]) {
    // 检查是否有子分类
    for (const id of ids) {
      const count = await this.categoryEntity.count({
        where: { parentId: id },
      });

      if (count > 0) {
        throw new Error('该分类下有子分类，无法删除');
      }
    }

    return super.delete(ids);
  }

  /**
   * 获取分类树形结构
   * @param regionId 可选地区ID
   */
  async tree(regionId?: number) {
    const query: any = {};
    if (regionId) {
      query.regionId = regionId;
    }

    // 获取所有分类
    const categories = await this.categoryEntity.find({
      where: query,
      order: {
        orderNum: 'ASC', // 按排序号升序
      },
    });

    // 构建树形结构
    const buildTree = (parentId: number | null = null) => {
      return categories
        .filter(item => item.parentId === parentId)
        .map(item => {
          const children = buildTree(item.id);
          return {
            ...item,
            children: children.length > 0 ? children : undefined,
          };
        });
    };

    return buildTree(null);
  }

  /**
   * 分类移动操作
   * @param id 分类ID
   * @param action 移动方向 up|down|top|bottom
   */
  async move(id: number, action: 'up' | 'down' | 'top' | 'bottom') {
    // 查询当前分类信息
    const curr = await this.categoryEntity.findOne({
      where: { id },
    });

    if (!curr) {
      throw new Error('分类不存在');
    }

    // 获取同级分类，按排序号排序
    const siblings = await this.categoryEntity.find({
      where: { parentId: curr.parentId },
      order: {
        orderNum: 'ASC',
      },
    });

    // 当前分类的索引
    const index = siblings.findIndex(item => item.id === id);

    switch (action) {
      case 'up':
        if (index <= 0) return false; // 已经是第一个
        await this.swapOrder(curr, siblings[index - 1]);
        break;
      case 'down':
        if (index >= siblings.length - 1) return false; // 已经是最后一个
        await this.swapOrder(curr, siblings[index + 1]);
        break;
      case 'top':
        if (index === 0) return false; // 已经是第一个
        await this.moveToEdge(curr, siblings, true);
        break;
      case 'bottom':
        if (index === siblings.length - 1) return false; // 已经是最后一个
        await this.moveToEdge(curr, siblings, false);
        break;
    }

    return true;
  }

  /**
   * 交换两个分类的排序号
   */
  private async swapOrder(a: CategoryEntity, b: CategoryEntity) {
    const temp = a.orderNum;
    await this.categoryEntity.update(a.id, { orderNum: b.orderNum });
    await this.categoryEntity.update(b.id, { orderNum: temp });
  }

  /**
   * 将分类移动到顶部或底部
   */
  private async moveToEdge(
    curr: CategoryEntity,
    siblings: CategoryEntity[],
    isTop: boolean
  ) {
    if (isTop) {
      // 移到顶部，设置比第一个还小的排序号
      const firstOrderNum = siblings[0].orderNum;
      await this.categoryEntity.update(curr.id, {
        orderNum: firstOrderNum - 10,
      });
    } else {
      // 移到底部，设置比最后一个还大的排序号
      const lastOrderNum = siblings[siblings.length - 1].orderNum;
      await this.categoryEntity.update(curr.id, {
        orderNum: lastOrderNum + 10,
      });
    }
  }
}
