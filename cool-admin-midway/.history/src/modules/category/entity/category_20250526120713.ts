import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 分类信息
 */
@Entity('category')
export class CategoryEntity extends BaseEntity {
  @Column({ comment: '分类名称', length: 50 })
  name: string;

  @Index()
  @Column({ comment: '父级ID', nullable: true, type: 'int' })
  parentId: number;

  @Column({ comment: '分类层级', default: 1, type: 'int' })
  level: number;

  @Column({ comment: '排序号', type: 'int', default: 0 })
  orderNum: number;

  @Column({ comment: '分类图标', nullable: true, length: 255 })
  icon: string;

  @Column({ comment: '分类描述', nullable: true, length: 500 })
  description: string;

  @Column({
    comment: '状态',
    default: 1,
    type: 'tinyint',
    dict: ['禁用', '启用'],
  })
  status: number;

  @Index()
  @Column({ comment: '地区ID', nullable: true, type: 'int' })
  regionId: number;

  @Column({ comment: '显示模板', nullable: true, length: 100 })
  displayTemplate: string;

  @Column({ comment: '路径层级ID(,分隔)', nullable: true, length: 255 })
  pathIds: string;
}
