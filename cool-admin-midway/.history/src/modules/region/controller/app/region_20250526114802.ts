import { Inject, Controller, Get, Param } from '@midwayjs/core';
import { BaseController } from '@cool-midway/core';
import { RegionService } from '../../service/region';

/**
 * 地区查询接口
 */
@Controller('/app/region')
export class AppRegionController extends BaseController {
  @Inject()
  regionService: RegionService;

  /**
   * 获取省份列表
   */
  @Get('/provinces')
  async provinces() {
    return this.ok(await this.regionService.provinces());
  }

  /**
   * 获取城市列表
   */
  @Get('/cities/:provinceId')
  async cities(@Param('provinceId') provinceId: number) {
    return this.ok(await this.regionService.cities(provinceId));
  }
}
