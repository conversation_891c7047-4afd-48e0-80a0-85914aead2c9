import { Inject, Controller, Get, Param } from '@midwayjs/core';
import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { RegionEntity } from '../../entity/region';
import { RegionService } from '../../service/region';

/**
 * 地区管理
 */
@Controller('/admin/region')
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: RegionEntity,
})
export class RegionController extends BaseController {
  @Inject()
  regionService: RegionService;

  /**
   * 获取地区树形结构
   */
  @Get('/tree')
  async tree() {
    return this.ok(await this.regionService.tree());
  }

  /**
   * 获取省份列表
   */
  @Get('/provinces')
  async provinces() {
    return this.ok(await this.regionService.provinces());
  }

  /**
   * 获取城市列表
   */
  @Get('/cities/:provinceId')
  async cities(@Param('provinceId') provinceId: number) {
    return this.ok(await this.regionService.cities(provinceId));
  }

  /**
   * 获取地区路径
   */
  @Get('/path/:id')
  async path(@Param('id') id: number) {
    return this.ok(await this.regionService.getRegionPath(id));
  }
}
