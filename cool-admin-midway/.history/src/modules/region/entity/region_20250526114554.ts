import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 地区信息
 */
@Entity('region')
export class RegionEntity extends BaseEntity {
  @Column({ comment: '地区名称', length: 50 })
  name: string;

  @Column({
    comment: '地区类型',
    type: 'tinyint',
    dict: ['省份', '直辖市', '城市'],
  })
  type: number;

  @Index()
  @Column({ comment: '父级地区ID', nullable: true, type: 'int' })
  parentId: number;

  @Column({ comment: '地区编码', nullable: true, length: 20 })
  code: string;

  @Column({ comment: '排序号', type: 'int', default: 0 })
  orderNum: number;
}
