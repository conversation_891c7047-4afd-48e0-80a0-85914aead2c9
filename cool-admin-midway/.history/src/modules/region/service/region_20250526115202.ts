import { Provide } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { RegionEntity } from '../entity/region';
import { Repository } from 'typeorm';

/**
 * 地区服务
 */
@Provide()
export class RegionService extends BaseService {
  @InjectEntityModel(RegionEntity)
  regionEntity: Repository<RegionEntity>;

  /**
   * 获取地区树形结构
   */
  async tree() {
    // 获取所有地区
    const regions = await this.regionEntity.find({
      order: {
        orderNum: 'ASC', // 按排序号升序
      },
    });

    // 构建树形结构
    const buildTree = (parentId: number | null = null) => {
      return regions
        .filter(item => item.parentId === parentId)
        .map(item => {
          const children = buildTree(item.id);
          return {
            ...item,
            children: children.length > 0 ? children : undefined,
          };
        });
    };

    return buildTree(null);
  }

  /**
   * 获取省份列表
   */
  async provinces() {
    return this.regionEntity.find({
      where: {
        type: 0, // 省份
      },
      order: {
        orderNum: 'ASC',
      },
    });
  }

  /**
   * 获取城市列表
   * @param provinceId 省份ID
   */
  async cities(provinceId: number) {
    return this.regionEntity.find({
      where: {
        parentId: provinceId,
        type: 2, // 城市
      },
      order: {
        orderNum: 'ASC',
      },
    });
  }

  /**
   * 根据ID获取完整地区路径
   * @param id 地区ID
   */
  async getRegionPath(id: number) {
    const result = [];
    let currentId = id;

    while (currentId) {
      const region = await this.regionEntity.findOne({
        where: { id: currentId },
      });

      if (!region) break;

      result.unshift(region);
      currentId = region.parentId;
    }

    return result;
  }
}
