---
description: 即时通讯(Socket)
globs: 
alwaysApply: false
---
# 即时通讯(Socket)

`cool-admin`即时通讯功能基于[Socket.io(v4)](mdc:https:/socket.io/docs/v4)开发，[midwayjs 官方 Socket.io 文档](mdc:http:/midwayjs.org/docs/extensions/socketio)

## 配置

`configuration.ts`

```ts
import * as socketio from "@midwayjs/socketio";

@Configuration({
  imports: [
    // socketio http://www.midwayjs.org/docs/extensions/socketio
    socketio,
  ],
  importConfigs: [join(__dirname, "./config")],
})
export class ContainerLifeCycle {
  @App()
  app: koa.Application;

  async onReady() {}
}
```

## 配置`config/config.default.ts`

需要配置 redis 适配器，让进程之间能够进行通讯

```ts
import { CoolConfig, MODETYPE } from "@cool-midway/core";
import { MidwayConfig } from "@midwayjs/core";
import * as fsStore from "@cool-midway/cache-manager-fs-hash";
import { createAdapter } from "@socket.io/redis-adapter";
// @ts-ignore
import Redis from "ioredis";

const redis = {
  host: "127.0.0.1",
  port: 6379,
  password: "",
  db: 0,
};

const pubClient = new Redis(redis);
const subClient = pubClient.duplicate();

export default {
  // ...
  // socketio
  socketIO: {
    upgrades: ["websocket"], // 可升级的协议
    adapter: createAdapter(pubClient, subClient),
  },
} as MidwayConfig;
```

## 服务端

```ts
import {
  WSController,
  OnWSConnection,
  Inject,
  OnWSMessage,
} from "@midwayjs/core";
import { Context } from "@midwayjs/socketio";
/**
 * 测试
 */
@WSController("/")
export class HelloController {
  @Inject()
  ctx: Context;

  // 客户端连接
  @OnWSConnection()
  async onConnectionMethod() {
    console.log("on client connect", this.ctx.id);
    console.log("参数", this.ctx.handshake.query);
    this.ctx.emit("data", "连接成功");
  }

  // 消息事件
  @OnWSMessage("myEvent")
  async gotMessage(data) {
    console.log("on data got", this.ctx.id, data);
  }
}
```

## 客户端

```ts
const io = require("socket.io-client");

const socket = io("http://127.0.0.1:8001", {
  auth: {
    token: "xxx",
  },
});

socket.on("data", (msg) => {
  console.log("服务端消息", msg);
});
```

## 注意事项

如果部署为多线程的，为了让进程之间能够进行通讯，需要配置 redis 适配器，[配置方式](mdc:http:/midwayjs.org/docs/extensions/socketio#%E9%85%8D%E7%BD%AE-redis-%E9%80%82%E9%85%8D%E5%99%A8)

```ts
// src/config/config.default
import { createRedisAdapter } from "@midwayjs/socketio";

export default {
  // ...
  socketIO: {
    adapter: createRedisAdapter({ host: "127.0.0.1", port: 6379 }),
  },
};
```
