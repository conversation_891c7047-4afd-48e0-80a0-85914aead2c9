---
description: 事件(Event)
globs: 
alwaysApply: false
---
# 事件(Event)
# 事件(Event)

事件是开发过程中经常使用到的功能，我们经常利用它来做一些解耦的操作。如：更新了用户信息，其他需要更新相关信息的操作自行监听更新等

## 新建监听

```ts
import { Provide, Scope, ScopeEnum } from "@midwayjs/core";
import { CoolEvent, Event } from "@cool-midway/core";

/**
 * 接收事件
 */
@CoolEvent()
export class DemoEvent {
  /**
   * 根据事件名接收事件
   * @param msg
   * @param a
   */
  @Event("updateUser")
  async updateUser(msg, a) {
    console.log("ImEvent", "updateUser", msg, a);
  }
}
```

## 发送事件

```ts
import { Get, Inject, Provide } from "@midwayjs/core";
import {
  CoolController,
  BaseController,
  CoolEventManager,
} from "@cool-midway/core";

/**
 * 事件
 */
@CoolController()
export class DemoEventController extends BaseController {
  @Inject()
  coolEventManager: CoolEventManager;

  /**
   * 发送事件
   */
  @Get("/send")
  public async send() {
    this.coolEventManager.emit("updateUser", { a: 1 }, 12);
  }
}
```

## 多进程通信

当你的项目利用如`pm2`等工具部署为 cluster 模式的时候，你的项目会有多个进程，这时候你的事件监听和发送只会在当前进程内有效，如果你需要触发到所有或者随机一个进程，需要使用多进程通信，这里我们提供了一个简单的方式来实现多进程通信。

需要根据你的业务需求来使用该功能！！！

```ts
import { Get, Inject, Provide } from "@midwayjs/core";
import {
  CoolController,
  BaseController,
  CoolEventManager,
} from "@cool-midway/core";

/**
 * 事件
 */
@Provide()
@CoolController()
export class DemoEventController extends BaseController {
  @Inject()
  coolEventManager: CoolEventManager;

  @Post("/global", { summary: "全局事件，多进程都有效" })
  async global() {
    await this.coolEventManager.globalEmit("demo", false, { a: 2 }, 1);
    return this.ok();
  }
}
```

**globalEmit**

```ts
/**
 * 发送全局事件
 * @param event 事件
 * @param random 是否随机一个
 * @param args 参数
 * @returns
 */
globalEmit(event: string, random?: boolean, ...args: any[])
```

# 事件处理开发规则

## 基本规范

- 事件处理文件命名使用下划线法，如：`data_change.event.ts`
- 事件处理程序应放在模块的`event`目录下
- 使用`@OnEvent`装饰器订阅事件

## 事件类型

### 1. 基础事件处理

```typescript
import { Provide } from '@midwayjs/core';
import { OnEvent, EventEmitter } from '@cool-midway/core';
import { Inject } from '@midwayjs/core';
import { XXXService } from '../service/xxx';

/**
 * 描述
 */
@Provide()
export class XXXEvent {
  @Inject()
  xxxService: XXXService;
  
  @Inject()
  eventEmitter: EventEmitter;
  
  /**
   * 监听XXX创建事件
   */
  @OnEvent('xxx.create')
  async onXXXCreate(params) {
    // 处理XXX创建事件
    console.log('XXX创建事件触发', params);
    
    // 调用服务处理业务逻辑
    await this.xxxService.handleCreate(params);
  }
  
  /**
   * 手动触发事件示例
   */
  async triggerEvent() {
    // 触发XXX更新事件
    this.eventEmitter.emit('xxx.update', { id: 1, data: {} });
  }
}
```

### 2. 系统事件监听

监听系统内置事件：

```typescript
import { Provide } from '@midwayjs/core';
import { OnEvent } from '@cool-midway/core';

/**
 * 系统事件监听
 */
@Provide()
export class SystemEvent {
  /**
   * 应用启动完成事件
   */
  @OnEvent('app.ready')
  async onAppReady() {
    console.log('应用启动完成');
  }
  
  /**
   * 用户登录事件
   */
  @OnEvent('user.login')
  async onUserLogin(user) {
    console.log('用户登录', user);
  }
}
```

### 3. 事件通信

跨模块事件通信：

```typescript
// 模块A中触发事件
@Provide()
export class ModuleAService {
  @Inject()
  eventEmitter: EventEmitter;
  
  async process() {
    // 业务处理
    
    // 触发数据变更事件，供其他模块监听
    this.eventEmitter.emit('moduleA.dataChange', { 
      id: 1, 
      type: 'update' 
    });
  }
}

// 模块B中监听事件
@Provide()
export class ModuleBEvent {
  /**
   * 监听模块A的数据变更事件
   */
  @OnEvent('moduleA.dataChange')
  async onModuleADataChange(data) {
    console.log('模块A数据变更', data);
    
    // 进行响应处理
  }
}
```

## 最佳实践

- 事件名称使用点号(.)分隔不同级别，如：`module.action.subaction`
- 避免在事件处理中执行长时间运行的任务，可以考虑使用队列
- 对于需要响应的事件，可以返回处理结果
- 处理事件时应捕获异常，避免影响其他事件处理程序
