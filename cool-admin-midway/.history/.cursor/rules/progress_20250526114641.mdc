---
description: 
globs: 
alwaysApply: true
---
# 项目开发进度(progress)

## 开发状态

| 模块名称 | 开发状态 | 完成功能 | 待开发功能 |
|---------|---------|---------|-----------|
| Region(地区) | 未开始 | - | 地区模型、省市区数据、地区管理API |
| Category(分类) | 未开始 | - | 分类管理、层级结构 |
| Subject(科目) | 未开始 | - | 科目管理、地域关联、价格方案 |
| Question(题库) | 未开始 | - | 题目管理、选项管理、省份关联 |
| User(用户) | 未开始 | - | 用户管理、微信绑定、地域关联 |
| Order(订单) | 未开始 | - | 订单管理、支付记录、激活码 |
| Study(学习) | 未开始 | - | 学习记录、错题收藏、考试系统 |

## 开发优先级

项目开发将按照以下顺序进行：

1. **地区模块(Region)** - 基础数据，其他模块依赖
2. **内容体系模块**:
   - 分类管理(Category)
   - 科目管理(Subject)
   - 题库管理(Question)
   - 题目选项(QuestionOption)
   - 价格方案(SubjectPrice)
3. **用户体系模块(User)**
4. **交易模块(Order)**
5. **学习模块(Study)**

## 开发计划

### Phase 1: 基础数据层

- [ ] 地区模块开发
  - [ ] 创建Region实体
  - [ ] 导入全国省市区数据
  - [ ] 开发地区管理API

### Phase 2: 内容体系

- [ ] 分类模块开发
  - [ ] 创建Category实体
  - [ ] 开发分类管理API

- [ ] 科目模块开发
  - [ ] 创建Subject实体
  - [ ] 创建SubjectPrice实体 
  - [ ] 开发科目管理API
  - [ ] 开发地域化显示功能

- [ ] 题库模块开发
  - [ ] 创建Question实体
  - [ ] 创建QuestionOption实体
  - [ ] 开发题目管理API
  - [ ] 实现地域关联

### Phase 3: 用户与交易

待基础数据层和内容体系开发完成后规划

## 本地化显示功能

项目有一个关键业务需求是基于用户所在地域(省市)的内容本地化显示：

- 根据用户所在地域智能展示相应的考题内容
- 通过显示模板(如`{city}{name}`)实现本地化展示
- 底层题库按省份划分，不同城市用户可以看到对应地区的内容

## 技术架构说明

- 后端框架：Midway.js + Cool-Admin 8.x
- 数据库：TypeORM 0.3.20 (不使用外键关系)
- 开发语言：TypeScript

## 最后更新

> 更新时间: 2023-06-01
> 开发状态: 准备开始，暂无功能完成
