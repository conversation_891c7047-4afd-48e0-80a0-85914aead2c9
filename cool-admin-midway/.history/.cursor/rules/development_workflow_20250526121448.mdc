---
description: 
globs: 
alwaysApply: true
---
# 开发工作流程规则(development_workflow)

## 模块开发流程

为确保项目进度透明且准确，每个模块的开发必须遵循以下工作流程：

### 1. 开发前准备

- **检查项目进度**：查阅 `.cursor/rules/progress.mdc` 文件，了解当前项目进度和待开发模块
- **确认开发优先级**：按照进度文件中的开发优先级顺序执行
- **了解依赖关系**：确认当前模块是否依赖其他模块，依赖模块是否已经完成

### 2. 开发过程

- **参考已完成模块**：新模块开发应参考已完成的同类模块的架构和模式
- **遵循代码规范**：严格遵守项目的代码风格和命名规范
- **完成基本功能点**：
  - 创建实体（entity）
  - 开发服务层（service）
  - 实现控制器（controller）
  - 编写初始化数据（db.json）
  - 配置模块信息（config.ts）

### 3. 开发完成后

- **更新进度文件**：
  - 修改 `.cursor/rules/progress.mdc` 中相应模块的开发状态
  - 更新已完成功能列表
  - 更新"最后更新"部分的时间和状态描述
  - 将开发计划中的相应任务标记为完成 [x]
  
- **代码审核**：
  - 确保所有功能点已实现
  - 检查代码质量和一致性
  - 验证与其他模块的集成

### 4. 准备下一个模块

- **查看更新后的进度文件**，确定下一个要开发的模块
- **分析下一个模块的需求和依赖关系**

## 模块开发检查点

每个模块开发完成后，检查以下项目是否已完成：

- [ ] 实体设计（entity）
- [ ] 核心业务服务（service）
- [ ] 管理端API接口（admin controller）
- [ ] 应用端API接口（app controller）
- [ ] 模块配置（config.ts）
- [ ] 初始化数据（db.json，如需要）
- [ ] 单元测试（如适用）
- [ ] 项目进度更新（progress.mdc）

## 进度文件更新模板

```markdown
| 模块名称 | 开发状态 | 完成功能 | 待开发功能 |
|---------|---------|---------|-----------|
| 模块名 | 已完成/进行中/未开始 | 功能1、功能2... | 待开发功能... |

...

- [x] 已完成模块
  - [x] 已完成功能点1
  - [x] 已完成功能点2
  
## 最后更新

> 更新时间: YYYY-MM-DD
> 开发状态: 已完成XXX模块的全部功能，包括A、B、C等。下一步将开始开发YYY模块。
```

遵循此工作流程将确保项目进度始终保持最新，且所有团队成员都能清晰了解项目当前状态和下一步计划。
