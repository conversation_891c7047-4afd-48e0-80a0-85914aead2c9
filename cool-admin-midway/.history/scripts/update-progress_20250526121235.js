#!/usr/bin/env node

/**
 * 项目进度更新脚本
 * 使用方法：node scripts/update-progress.js <模块名> <开发状态> <完成功能> [<待开发功能>]
 * 例如：node scripts/update-progress.js Subject 已完成 "实体设计、API开发" "-"
 */

const fs = require('fs');
const path = require('path');

// 进度规则文件路径
const PROGRESS_FILE = path.join(__dirname, '../.cursor/rules/progress.mdc');

// 获取命令行参数
const [, , moduleName, status, completedFeatures, pendingFeatures = '-'] = process.argv;

if (!moduleName || !status || !completedFeatures) {
  console.error('请提供正确的参数: <模块名> <开发状态> <完成功能> [<待开发功能>]');
  process.exit(1);
}

// 读取当前进度文件
try {
  const content = fs.readFileSync(PROGRESS_FILE, 'utf-8');
  
  // 更新模块状态
  const updatedContent = updateModuleStatus(
    content, 
    moduleName, 
    status, 
    completedFeatures, 
    pendingFeatures
  );
  
  // 更新开发计划的勾选状态
  const finalContent = updateDevelopmentPlan(updatedContent, moduleName, status === '已完成');
  
  // 更新最后更新时间和状态
  const result = updateLastUpdateSection(finalContent, moduleName, status, completedFeatures);
  
  // 写入文件
  fs.writeFileSync(PROGRESS_FILE, result, 'utf-8');
  console.log(`✅ 成功更新 ${moduleName} 模块的开发状态`);
} catch (error) {
  console.error('更新进度文件失败:', error);
  process.exit(1);
}

/**
 * 更新模块状态表格
 */
function updateModuleStatus(content, moduleName, status, completedFeatures, pendingFeatures) {
  const regex = new RegExp(`\\|\\s*${moduleName}\\([^)]+\\)\\s*\\|[^|]*\\|[^|]*\\|[^|]*\\|`, 'i');
  const replacement = `| ${moduleName}(${getChineseName(moduleName)}) | ${status} | ${completedFeatures} | ${pendingFeatures} |`;
  
  return content.replace(regex, replacement);
}

/**
 * 更新开发计划的勾选状态
 */
function updateDevelopmentPlan(content, moduleName, isCompleted) {
  // 查找该模块在开发计划中的位置
  const moduleSection = `- [${isCompleted ? 'x' : ' '}] ${moduleName}模块开发`;
  const regex = new RegExp(`- \\[[ x]\\] ${moduleName}模块开发`, 'i');
  
  return content.replace(regex, moduleSection);
}

/**
 * 更新最后更新部分
 */
function updateLastUpdateSection(content, moduleName, status, completedFeatures) {
  const today = new Date().toISOString().split('T')[0].replace(/-/g, '-');
  const chineseName = getChineseName(moduleName);
  
  let nextModule = '';
  if (status === '已完成') {
    nextModule = getNextModule(moduleName);
  }
  
  const nextModuleText = nextModule ? `下一步将开始开发${getChineseName(nextModule)}模块。` : '';
  
  const updateText = `
## 最后更新

> 更新时间: ${today}
> 开发状态: ${status === '已完成' ? `已完成${chineseName}模块全部功能，包括${completedFeatures}。${nextModuleText}` : `${chineseName}模块开发中，当前进度：${completedFeatures}。`}`;

  // 替换最后更新部分
  const lastUpdateRegex = /## 最后更新\n\n>[^\n]*\n>[^\n]*/;
  return content.replace(lastUpdateRegex, updateText.trim());
}

/**
 * 获取模块的中文名称
 */
function getChineseName(moduleName) {
  const moduleMap = {
    'Region': '地区',
    'Category': '分类',
    'Subject': '科目',
    'Question': '题库',
    'User': '用户',
    'Order': '订单',
    'Study': '学习'
  };
  
  return moduleMap[moduleName] || moduleName;
}

/**
 * 获取下一个要开发的模块
 */
function getNextModule(currentModule) {
  const moduleSequence = ['Region', 'Category', 'Subject', 'Question', 'User', 'Order', 'Study'];
  const currentIndex = moduleSequence.indexOf(currentModule);
  
  if (currentIndex >= 0 && currentIndex < moduleSequence.length - 1) {
    return moduleSequence[currentIndex + 1];
  }
  
  return '';
} 