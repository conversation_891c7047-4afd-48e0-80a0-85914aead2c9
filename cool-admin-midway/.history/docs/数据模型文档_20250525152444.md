# 考理论教育平台 - 数据模型文档

## 核心实体关系

```
用户(User) ──┬── 订单(Order)
            ├── 学习记录(StudyRecord)
            ├── 考试记录(ExamRecord)
            ├── 错题记录(WrongQuestion)
            ├── 收藏题目(FavoriteQuestion)
            └── 分销商(Distributor)

科目(Subject) ──┬── 题目(Question)
               ├── 考试(Exam)
               ├── 价格方案(SubjectPrice)
               └── 订单(Order)

题目(Question) ──┬── 选项(QuestionOption)
                ├── 错题记录(WrongQuestion)
                └── 收藏记录(FavoriteQuestion)
```

## 用户体系模型

### 用户基础信息
```prisma
model User {
  id        String   @id @default(cuid())
  nickname  String?  @db.VarChar(50)
  avatar    String?  @db.VarChar(500)
  phone     String?  @unique @db.VarChar(20)
  city      String?  @db.VarChar(50)
  province  String?  @db.VarChar(50)
  status    Int      @default(1) @db.SmallInt // 1-正常 2-禁用
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关系
  wechatAccounts WechatAccount[]
  orders         Order[]
  studyRecords   StudyRecord[]
  examRecords    ExamRecord[]
  wrongQuestions WrongQuestion[]
  favoriteQuestions FavoriteQuestion[]
  distributor    Distributor?

  @@map("users")
}
```

### 微信账号信息
```prisma
model WechatAccount {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  unionId   String?  @unique @map("union_id") @db.VarChar(100)
  openId    String   @unique @map("open_id") @db.VarChar(100)
  platform  Int      @db.SmallInt // 1-小程序 2-公众号
  nickname  String?  @db.VarChar(50)
  avatar    String?  @db.VarChar(500)
  createdAt DateTime @default(now()) @map("created_at")

  // 关系
  user      User     @relation(fields: [userId], references: [id])

  @@map("wechat_accounts")
}
```

### 分销商信息
```prisma
model Distributor {
  id          String   @id @default(cuid())
  userId      String   @unique @map("user_id")
  parentId    String?  @map("parent_id")
  level       Int      @default(1) @db.SmallInt // 分销层级
  commission  Decimal  @default(0.15) @db.Decimal(5, 4) // 佣金比例
  totalEarned Decimal  @default(0) @db.Decimal(10, 2) // 累计收益
  balance     Decimal  @default(0) @db.Decimal(10, 2) // 可提现余额
  status      Int      @default(1) @db.SmallInt // 1-正常 2-禁用
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关系
  user        User         @relation(fields: [userId], references: [id])
  parent      Distributor? @relation("DistributorHierarchy", fields: [parentId], references: [id])
  children    Distributor[] @relation("DistributorHierarchy")
  withdrawRecords WithdrawRecord[]

  @@map("distributors")
}
```

## 内容体系模型

### 分类信息
```prisma
model Category {
  id        String   @id @default(cuid())
  name      String   @db.VarChar(100)
  parentId  String?  @map("parent_id")
  level     Int      @default(1) @db.SmallInt
  orderNum  Int      @default(0) @map("order_num")
  status    Int      @default(1) @db.SmallInt
  createdAt DateTime @default(now()) @map("created_at")

  // 关系
  parent    Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children  Category[] @relation("CategoryHierarchy")
  subjects  Subject[]

  @@map("categories")
}
```

### 科目信息
```prisma
model Subject {
  id           String   @id @default(cuid())
  name         String   @db.VarChar(200)
  description  String?  @db.Text
  categoryId   String   @map("category_id")
  displayTemplate String? @map("display_template") @db.VarChar(200) // 显示模板
  questionCount Int     @default(0) @map("question_count")
  orderNum     Int      @default(0) @map("order_num")
  status       Int      @default(1) @db.SmallInt // 1-上架 2-下架
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // 关系
  category     Category        @relation(fields: [categoryId], references: [id])
  questions    Question[]
  prices       SubjectPrice[]
  orders       Order[]
  studyRecords StudyRecord[]
  examRecords  ExamRecord[]

  @@map("subjects")
}
```

### 科目价格方案
```prisma
model SubjectPrice {
  id        String   @id @default(cuid())
  subjectId String   @map("subject_id")
  name      String   @db.VarChar(100) // 套餐名称
  price     Decimal  @db.Decimal(10, 2)
  validDays Int      @map("valid_days") // 有效天数
  isDefault Boolean  @default(false) @map("is_default")
  status    Int      @default(1) @db.SmallInt
  createdAt DateTime @default(now()) @map("created_at")

  // 关系
  subject   Subject  @relation(fields: [subjectId], references: [id])
  orders    Order[]

  @@map("subject_prices")
}
```

### 题目信息
```prisma
model Question {
  id        String   @id @default(cuid())
  subjectId String   @map("subject_id")
  type      Int      @db.SmallInt // 1-单选 2-多选 3-判断
  content   String   @db.Text
  analysis  String?  @db.Text // 解析
  difficulty Int     @default(1) @db.SmallInt // 1-简单 2-中等 3-困难
  orderNum  Int      @default(0) @map("order_num")
  status    Int      @default(1) @db.SmallInt
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关系
  subject           Subject            @relation(fields: [subjectId], references: [id])
  options           QuestionOption[]
  studyRecords      StudyRecord[]
  wrongQuestions    WrongQuestion[]
  favoriteQuestions FavoriteQuestion[]

  @@map("questions")
}
```

### 题目选项
```prisma
model QuestionOption {
  id         String   @id @default(cuid())
  questionId String   @map("question_id")
  content    String   @db.Text
  isCorrect  Boolean  @default(false) @map("is_correct")
  orderNum   Int      @default(0) @map("order_num")
  createdAt  DateTime @default(now()) @map("created_at")

  // 关系
  question   Question @relation(fields: [questionId], references: [id])

  @@map("question_options")
}
```

## 交易体系模型

### 订单信息
```prisma
model Order {
  id          String   @id @default(cuid())
  orderNo     String   @unique @map("order_no") @db.VarChar(32)
  userId      String   @map("user_id")
  subjectId   String   @map("subject_id")
  priceId     String   @map("price_id")
  amount      Decimal  @db.Decimal(10, 2)
  payAmount   Decimal  @default(0) @map("pay_amount") @db.Decimal(10, 2)
  status      Int      @default(0) @db.SmallInt // 0-待支付 1-已支付 2-已取消 3-异常
  validUntil  DateTime? @map("valid_until") // 权限有效期
  distributorId String? @map("distributor_id") // 推广人
  paidAt      DateTime? @map("paid_at")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关系
  user        User           @relation(fields: [userId], references: [id])
  subject     Subject        @relation(fields: [subjectId], references: [id])
  price       SubjectPrice   @relation(fields: [priceId], references: [id])
  payments    PaymentRecord[]

  @@map("orders")
}
```

### 支付记录
```prisma
model PaymentRecord {
  id           String   @id @default(cuid())
  orderId      String   @map("order_id")
  paymentType  Int      @db.SmallInt // 1-微信支付 2-支付宝支付 3-激活码
  amount       Decimal  @db.Decimal(10, 2)
  tradeNo      String?  @map("trade_no") @db.VarChar(100)
  thirdTradeNo String?  @map("third_trade_no") @db.VarChar(100)
  activationCode String? @map("activation_code") @db.VarChar(50)
  status       Int      @default(0) @db.SmallInt // 0-待支付 1-支付成功 2-支付失败
  paidAt       DateTime? @map("paid_at")
  failReason   String?  @map("fail_reason") @db.Text
  createdAt    DateTime @default(now()) @map("created_at")

  // 关系
  order        Order    @relation(fields: [orderId], references: [id])

  @@map("payment_records")
}
```

### 激活码管理
```prisma
model ActivationCode {
  id        String   @id @default(cuid())
  code      String   @unique @db.VarChar(20)
  subjectId String   @map("subject_id")
  validDays Int      @map("valid_days")
  usedBy    String?  @map("used_by") // 使用者用户ID
  usedAt    DateTime? @map("used_at")
  status    Int      @default(0) @db.SmallInt // 0-未使用 1-已使用 2-已过期 3-已禁用
  createdAt DateTime @default(now()) @map("created_at")
  expiredAt DateTime? @map("expired_at")

  @@map("activation_codes")
}
```

## 学习体系模型

### 学习记录
```prisma
model StudyRecord {
  id          String   @id @default(cuid())
  userId      String   @map("user_id")
  subjectId   String   @map("subject_id")
  questionId  String   @map("question_id")
  practiceType Int     @db.SmallInt // 1-顺序 2-题型 3-错题 4-随机 5-收藏 6-搜题
  userAnswer  String?  @map("user_answer") @db.Text // 用户答案
  isCorrect   Boolean? @map("is_correct")
  timeSpent   Int      @default(0) @map("time_spent") // 答题耗时(秒)
  createdAt   DateTime @default(now()) @map("created_at")

  // 关系
  user        User     @relation(fields: [userId], references: [id])
  subject     Subject  @relation(fields: [subjectId], references: [id])
  question    Question @relation(fields: [questionId], references: [id])

  @@unique([userId, questionId])
  @@map("study_records")
}
```

### 考试记录
```prisma
model ExamRecord {
  id          String   @id @default(cuid())
  userId      String   @map("user_id")
  subjectId   String   @map("subject_id")
  score       Int      @default(0) // 考试得分
  totalScore  Int      @map("total_score") // 总分
  passScore   Int      @map("pass_score") // 及格分
  isPassed    Boolean  @default(false) @map("is_passed")
  timeSpent   Int      @map("time_spent") // 考试耗时(秒)
  answers     Json     // 答题详情
  startedAt   DateTime @map("started_at")
  finishedAt  DateTime? @map("finished_at")
  createdAt   DateTime @default(now()) @map("created_at")

  // 关系
  user        User     @relation(fields: [userId], references: [id])
  subject     Subject  @relation(fields: [subjectId], references: [id])

  @@map("exam_records")
}
```

### 错题记录
```prisma
model WrongQuestion {
  id          String   @id @default(cuid())
  userId      String   @map("user_id")
  questionId  String   @map("question_id")
  wrongCount  Int      @default(1) @map("wrong_count") // 错误次数
  lastWrongAt DateTime @map("last_wrong_at")
  isMastered  Boolean  @default(false) @map("is_mastered") // 是否已掌握
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关系
  user        User     @relation(fields: [userId], references: [id])
  question    Question @relation(fields: [questionId], references: [id])

  @@unique([userId, questionId])
  @@map("wrong_questions")
}
```

### 收藏题目
```prisma
model FavoriteQuestion {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  questionId String  @map("question_id")
  note      String?  @db.Text // 个人备注
  createdAt DateTime @default(now()) @map("created_at")

  // 关系
  user      User     @relation(fields: [userId], references: [id])
  question  Question @relation(fields: [questionId], references: [id])

  @@unique([userId, questionId])
  @@map("favorite_questions")
}
```

## 微信体系模型

### 微信消息记录
```prisma
model WechatMessage {
  id        String   @id @default(cuid())
  fromUser  String   @map("from_user") @db.VarChar(100)    // 发送方OpenID
  toUser    String   @map("to_user") @db.VarChar(100)      // 接收方OpenID
  msgType   String   @map("msg_type") @db.VarChar(20)      // 消息类型
  content   String?  @db.Text                              // 消息内容
  mediaId   String?  @map("media_id") @db.VarChar(100)     // 媒体文件ID
  event     String?  @db.VarChar(50)                       // 事件类型
  eventKey  String?  @map("event_key") @db.VarChar(100)    // 事件KEY
  msgId     String?  @map("msg_id") @db.VarChar(100)       // 消息ID
  createdAt DateTime @default(now()) @map("created_at")

  @@map("wechat_messages")
}
```

### 微信菜单配置
```prisma
model WechatMenu {
  id        String   @id @default(cuid())
  name      String   @db.VarChar(50)                       // 菜单名称
  type      String   @db.VarChar(20)                       // 菜单类型
  key       String?  @db.VarChar(100)                      // 菜单KEY
  url       String?  @db.VarChar(500)                      // 跳转URL
  appId     String?  @map("app_id") @db.VarChar(100)       // 小程序AppID
  pagePath  String?  @map("page_path") @db.VarChar(200)    // 小程序页面路径
  parentId  String?  @map("parent_id")                     // 父菜单ID
  orderNum  Int      @default(0) @map("order_num")         // 排序号
  status    Int      @default(1) @db.SmallInt              // 状态
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关系
  parent    WechatMenu?  @relation("MenuHierarchy", fields: [parentId], references: [id])
  children  WechatMenu[] @relation("MenuHierarchy")

  @@map("wechat_menus")
}
```

## 管理体系模型

### 管理员用户
```prisma
model AdminUser {
  id          String    @id @default(cuid())
  username    String    @unique @db.VarChar(50)        // 用户名
  password    String    @db.VarChar(255)               // 加密密码
  realName    String    @map("real_name") @db.VarChar(50)  // 真实姓名
  email       String?   @unique @db.VarChar(100)       // 邮箱
  phone       String?   @db.VarChar(20)                // 手机号
  avatar      String?   @db.VarChar(500)               // 头像
  role        String    @default("admin") @db.VarChar(20)  // 角色
  status      Int       @default(1) @db.SmallInt       // 状态：1-正常 2-禁用
  lastLogin   DateTime? @map("last_login")             // 最后登录时间
  loginCount  Int       @default(0) @map("login_count") // 登录次数
  createdBy   String?   @map("created_by")             // 创建人
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // 关系
  createdLogs OperationLog[] @relation("CreatedLogs")

  @@map("admin_users")
}
```

### 系统配置
```prisma
model SystemConfig {
  id          String   @id @default(cuid())
  key         String   @unique @db.VarChar(100)        // 配置键
  value       String   @db.Text                        // 配置值
  type        String   @default("string") @db.VarChar(20)  // 数据类型
  group       String   @default("system") @db.VarChar(50)  // 配置分组
  name        String   @db.VarChar(100)                // 配置名称
  description String?  @db.VarChar(500)                // 配置描述
  isPublic    Boolean  @default(false) @map("is_public")   // 是否公开
  isRequired  Boolean  @default(false) @map("is_required") // 是否必需
  sortOrder   Int      @default(0) @map("sort_order")  // 排序
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("system_configs")
}
```

### 操作日志
```prisma
model OperationLog {
  id        String   @id @default(cuid())
  adminId   String?  @map("admin_id")                  // 操作管理员ID
  userType  String   @map("user_type") @db.VarChar(20) // 用户类型：admin, user
  action    String   @db.VarChar(100)                  // 操作类型
  module    String   @db.VarChar(50)                   // 模块名称
  target    String?  @db.VarChar(100)                  // 操作目标
  content   String?  @db.Text                          // 操作内容
  ip        String?  @db.VarChar(50)                   // IP地址
  userAgent String?  @map("user_agent") @db.Text       // 用户代理
  result    String   @default("success") @db.VarChar(20) // 操作结果
  errorMsg  String?  @map("error_msg") @db.Text        // 错误信息
  createdAt DateTime @default(now()) @map("created_at")

  // 关系
  admin     AdminUser? @relation("CreatedLogs", fields: [adminId], references: [id])

  @@map("operation_logs")
}
```

### 佣金提现记录
```prisma
model WithdrawRecord {
  id            String   @id @default(cuid())
  distributorId String   @map("distributor_id")
  amount        Decimal  @db.Decimal(10, 2)
  fee           Decimal  @default(0) @db.Decimal(10, 2) // 手续费
  actualAmount  Decimal  @map("actual_amount") @db.Decimal(10, 2) // 实际到账金额
  withdrawType  Int      @map("withdraw_type") @db.SmallInt // 1-支付宝 2-微信 3-银行卡
  account       String   @db.VarChar(100) // 提现账号
  accountName   String   @map("account_name") @db.VarChar(50) // 账户名
  status        Int      @default(0) @db.SmallInt // 0-待审核 1-已通过 2-已拒绝 3-已到账
  remark        String?  @db.Text // 备注
  reviewedBy    String?  @map("reviewed_by") // 审核人
  reviewedAt    DateTime? @map("reviewed_at")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // 关系
  distributor   Distributor @relation(fields: [distributorId], references: [id])

  @@map("withdraw_records")
}
```
