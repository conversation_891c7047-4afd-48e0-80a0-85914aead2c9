# 考理论教育平台 - 用户流程文档

## 用户端核心流程

### 用户注册登录流程

```mermaid
graph TD
    A[用户打开小程序/公众号] --> B{是否已授权}
    B -->|否| C[点击授权登录]
    B -->|是| D[自动获取用户信息]

    C --> E[微信授权弹窗]
    E --> F{用户是否同意}
    F -->|拒绝| G[提示需要授权才能使用]
    F -->|同意| H[获取OpenID和UnionID]

    H --> I{检查是否已有账号}
    I -->|有| J[登录成功，更新登录信息]
    I -->|无| K[创建新用户账号]

    D --> L[检查用户状态]
    L --> M{用户状态正常}
    M -->|是| N[进入首页]
    M -->|否| O[显示账号异常提示]

    J --> N
    K --> P[获取用户位置信息]
    P --> Q[设置用户城市]
    Q --> N

    G --> A
    O --> R[联系客服]
```

**关键节点说明**：
1. **授权获取**：获取用户基本信息和UnionID
2. **账号打通**：通过UnionID关联小程序和公众号账号
3. **位置获取**：用于本地化科目显示
4. **状态检查**：确保用户账号正常可用

### 科目浏览购买流程

```mermaid
graph TD
    A[进入首页] --> B[显示科目分类]
    B --> C[用户选择分类]
    C --> D[显示科目列表]
    D --> E[本地化科目名称显示]
    E --> F[用户点击科目]

    F --> G[显示科目详情]
    G --> H{检查用户权限}
    H -->|已购买| I[显示"开始学习"按钮]
    H -->|未购买| J[显示价格方案]

    I --> K[进入学习页面]

    J --> L[用户选择套餐]
    L --> M[显示订单确认页]
    M --> N[显示购买须知：不支持退款]
    N --> O{用户确认购买}
    O -->|取消| G
    O -->|确认| P[选择支付方式]

    P --> Q{支付方式}
    Q -->|微信支付| R[调用微信支付]
    Q -->|支付宝支付| S[跳转支付宝H5]
    Q -->|激活码| T[输入激活码]

    R --> U[支付结果处理]
    S --> U
    T --> V[验证激活码]
    V --> W{激活码有效}
    W -->|是| X[开通权限]
    W -->|否| Y[显示错误提示]

    U --> Z{支付成功}
    Z -->|是| X
    Z -->|否| AA[支付失败处理]

    X --> BB[发送开通通知]
    BB --> CC[跳转学习页面]

    Y --> T
    AA --> P
```

**关键节点说明**：
1. **本地化显示**：根据用户城市显示科目名称
2. **权限检查**：避免重复购买
3. **支付确认**：明确告知不退款政策
4. **多种支付**：支持微信、支付宝、激活码
5. **异常处理**：支付失败、激活码无效等情况

### 学习练习流程

```mermaid
graph TD
    A[进入学习页面] --> B[显示学习进度]
    B --> C[选择练习模式]

    C --> D{练习模式}
    D -->|顺序练习| E[按题目顺序加载]
    D -->|题型练习| F[选择题型]
    D -->|错题练习| G[加载错题列表]
    D -->|随机练习| H[随机抽取题目]
    D -->|收藏练习| I[加载收藏题目]
    D -->|搜题模式| J[输入搜索关键词]

    E --> K[应用优先级算法]
    F --> L[选择具体题型]
    L --> K
    G --> K
    H --> K
    I --> K
    J --> M[搜索题目]
    M --> K

    K --> N[优先显示未答题目]
    N --> O[显示题目内容]
    O --> P[用户答题]

    P --> Q[提交答案]
    Q --> R[判断正误]
    R --> S{答案正确}
    S -->|正确| T[显示正确提示]
    S -->|错误| U[记录错题]

    T --> V[更新学习记录]
    U --> W[显示正确答案和解析]
    W --> V

    V --> X{是否继续}
    X -->|继续| Y[加载下一题]
    X -->|结束| Z[显示练习总结]

    Y --> N
    Z --> AA[更新学习进度]
    AA --> BB[推荐下次学习内容]
```

**关键节点说明**：
1. **优先级算法**：未答题目优先显示
2. **学习记录**：记录答题情况和耗时
3. **错题管理**：自动记录错题供后续复习
4. **进度跟踪**：实时更新学习进度
5. **智能推荐**：根据学习情况推荐内容

### 模拟考试流程

```mermaid
graph TD
    A[点击模拟考试] --> B{检查权限}
    B -->|无权限| C[提示购买科目]
    B -->|有权限| D{检查今日考试次数}
    D -->|超限| E[提示次数限制]
    D -->|未超限| F[显示考试说明]

    F --> G[用户确认开始]
    G --> H[初始化考试环境]
    H --> I[随机抽取题目]
    I --> J[开始计时]
    J --> K[显示第一题]

    K --> L[用户答题]
    L --> M[自动保存答案]
    M --> N{检测异常}
    N -->|网络中断| O[本地缓存答案]
    N -->|应用切换| P[记录切换次数]
    N -->|正常| Q[继续答题]

    O --> R[等待网络恢复]
    R --> S[同步答案到服务器]
    S --> Q

    P --> T{切换次数检查}
    T -->|<3次| U[警告提示]
    T -->|≥3次| V[强制交卷]
    U --> Q

    Q --> W{是否最后一题}
    W -->|否| X[下一题]
    W -->|是| Y[显示交卷确认]
    X --> K

    Y --> Z{用户确认交卷}
    Z -->|否| K
    Z -->|是| V

    V --> AA[停止计时]
    AA --> BB[计算成绩]
    BB --> CC[生成成绩报告]
    CC --> DD[保存考试记录]
    DD --> EE[发送成绩通知]
    EE --> FF[显示成绩页面]

    C --> GG[跳转购买页面]
    E --> HH[显示历史成绩]
```

**关键节点说明**：
1. **权限验证**：确保用户有考试权限
2. **次数限制**：防止频繁考试
3. **异常恢复**：网络中断、应用切换的处理
4. **防作弊**：切换应用次数限制
5. **成绩计算**：自动计算并生成报告

### 分销推广流程

```mermaid
graph TD
    A[用户申请分销] --> B[填写个人信息]
    B --> C[上传身份证照片]
    C --> D[填写收款信息]
    D --> E[提交申请]

    E --> F[等待审核]
    F --> G{审核结果}
    G -->|通过| H[获得分销资格]
    G -->|拒绝| I[显示拒绝原因]

    H --> J[生成专属推广码]
    J --> K[获取推广物料]
    K --> L[开始推广]

    L --> M[分享推广链接]
    M --> N[用户通过链接访问]
    N --> O[记录推广关系]
    O --> P[用户购买科目]
    P --> Q[计算佣金]
    Q --> R[佣金到账]

    R --> S{申请提现}
    S -->|是| T[填写提现信息]
    S -->|否| U[继续推广]

    T --> V[提交提现申请]
    V --> W[等待审核]
    W --> X{提现审核}
    X -->|通过| Y[提现到账]
    X -->|拒绝| Z[显示拒绝原因]

    I --> AA[修改信息重新申请]
    Z --> BB[修改信息重新申请]
```

**关键节点说明**：
1. **资格审核**：验证身份信息真实性
2. **推广跟踪**：准确记录推广关系
3. **佣金计算**：自动计算多级佣金
4. **提现管理**：支持多种提现方式
5. **风控机制**：防止虚假推广

## 管理端核心流程

### 内容管理流程

```mermaid
graph TD
    A[进入内容管理] --> B{选择管理类型}
    B -->|科目管理| C[科目列表页面]
    B -->|题库管理| D[题库列表页面]
    B -->|分类管理| E[分类树形结构]

    C --> F{科目操作}
    F -->|新增| G[填写科目信息]
    F -->|编辑| H[修改科目信息]
    F -->|删除| I[确认删除操作]
    F -->|上下架| J[切换科目状态]

    G --> K[设置价格方案]
    K --> L[配置显示模板]
    L --> M[保存科目信息]

    H --> N[更新科目信息]
    N --> O[记录操作日志]

    I --> P{确认删除}
    P -->|是| Q[检查关联数据]
    P -->|否| C
    Q --> R{有关联数据}
    R -->|是| S[提示无法删除]
    R -->|否| T[执行删除]

    D --> U{题目操作}
    U -->|批量导入| V[上传Excel文件]
    U -->|单个新增| W[填写题目信息]
    U -->|编辑题目| X[修改题目内容]
    U -->|删除题目| Y[确认删除]

    V --> Z[验证文件格式]
    Z --> AA{格式正确}
    AA -->|是| BB[解析题目数据]
    AA -->|否| CC[显示格式错误]

    BB --> DD[批量保存题目]
    DD --> EE[更新科目题目数量]

    W --> FF[添加题目选项]
    FF --> GG[设置正确答案]
    GG --> HH[保存题目]

    M --> O
    T --> O
    HH --> O
    EE --> O
    O --> II[刷新页面数据]
```

### 订单管理流程

```mermaid
graph TD
    A[进入订单管理] --> B[显示订单列表]
    B --> C{订单筛选}
    C -->|全部订单| D[显示所有订单]
    C -->|待支付| E[显示未支付订单]
    C -->|已支付| F[显示已支付订单]
    C -->|异常订单| G[显示异常订单]

    D --> H{订单操作}
    E --> H
    F --> H
    G --> H

    H -->|查看详情| I[显示订单详细信息]
    H -->|处理异常| J[异常订单处理]
    H -->|手动开通| K[手动开通权限]
    H -->|取消订单| L[取消订单操作]

    I --> M[查看支付记录]
    M --> N[查看用户信息]
    N --> O[查看科目信息]

    J --> P{异常类型}
    P -->|支付成功未开通| Q[检查支付状态]
    P -->|重复支付| R[处理重复支付]
    P -->|金额异常| S[核实支付金额]

    Q --> T{支付确实成功}
    T -->|是| U[手动开通权限]
    T -->|否| V[联系支付平台]

    R --> W[退还多余款项]
    W --> X[保留一个有效订单]

    S --> Y[人工核实]
    Y --> Z{金额正确}
    Z -->|是| U
    Z -->|否| AA[联系用户确认]

    K --> BB[选择开通科目]
    BB --> CC[设置有效期]
    CC --> DD[确认开通]
    DD --> EE[更新订单状态]
    EE --> FF[发送开通通知]

    L --> GG{确认取消}
    GG -->|是| HH[取消订单]
    GG -->|否| B
    HH --> II[更新订单状态]
    II --> JJ[发送取消通知]

    U --> KK[记录处理日志]
    FF --> KK
    JJ --> KK
    KK --> LL[刷新订单列表]
```

## 异常处理流程

### 支付异常处理
1. **支付超时**：30分钟后自动取消订单
2. **支付失败**：提示用户重新支付或更换支付方式
3. **重复支付**：自动退款或转为余额
4. **金额异常**：人工审核处理

### 考试异常处理
1. **网络中断**：本地缓存答案，恢复后同步
2. **应用切换**：记录次数，超限强制交卷
3. **系统故障**：保存当前进度，恢复后继续
4. **时间异常**：服务器时间为准，客户端时间仅参考

### 数据异常处理
1. **数据丢失**：从备份恢复
2. **数据不一致**：数据校验和修复
3. **并发冲突**：乐观锁处理
4. **系统错误**：错误日志记录和告警
