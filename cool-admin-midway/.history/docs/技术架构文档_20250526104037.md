# 考理论教育平台 - 技术架构文档

## 整体架构
采用 **Cool-Admin 全栈架构**，支持多端接入：

```
┌─────────────────────────────────────────────────────────────┐
│                        Nginx + 负载均衡                      │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┼───────────────────────────────────────┐
│                     │           前端层                       │
│  ┌─────────────────┐│┌─────────────────┐ ┌─────────────────┐ │
│  │   微信小程序     │││   微信公众号     │ │    管理后台      │ │
│  │(cool-admin-uni) │││(cool-admin-uni) │ │(cool-admin-vue) │ │
│  └─────────────────┘│└─────────────────┘ └─────────────────┘ │
│  ┌─────────────────┐│┌─────────────────┐                    │
│  │   H5/Web端      │││   移动端PWA     │                     │
│  │(cool-admin-uni) │││(cool-admin-uni) │                     │
│  └─────────────────┘│└─────────────────┘                     │
└─────────────────────┼───────────────────────────────────────┘
                      │
┌─────────────────────┼───────────────────────────────────────┐
│                     │           应用层                       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │          Node.js + Midway.js (cool-admin-midway 8.x)   │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │ │
│  │  │  用户模块    │ │  内容模块    │ │     支付模块        │ │ │
│  │  └─────────────┘ └─────────────┘ └─────────────────────┘ │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │ │
│  │  │ 微信接入模块 │ │  任务队列    │ │    数据分析模块      │ │ │
│  │  └─────────────┘ └─────────────┘ └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────┼───────────────────────────────────────┘
                      │
┌─────────────────────┼───────────────────────────────────────┐
│                     │           数据层                       │
│  ┌─────────────────┐│┌─────────────────┐ ┌─────────────────┐ │
│  │     MySQL       │││      Redis      │ │   本地存储       │ │
│  │   (主数据库)     │││     (缓存)      │ │  (文件存储)      │ │
│  └─────────────────┘│└─────────────────┘ └─────────────────┘ │
│                     │                                       │
└─────────────────────┼───────────────────────────────────────┘
```

## 技术选型

### 管理后台技术栈 (cool-admin-vue 8.x)

| 技术层 | 技术选择 | 版本 | 理由 |
|--------|----------|------|------|
| **核心框架** | cool-admin-vue | 8.x | 企业级中后台管理系统框架，低代码开发，提高开发效率 |
| **JavaScript框架** | Vue | 3.x | 渐进式框架，Composition API，学习成本低 |
| **构建工具** | Vite | 4.x+ | 极速构建，热更新快，开发体验佳 |
| **UI组件库** | Element Plus | 2.x | Vue生态最成熟的UI库，组件丰富 |
| **开发语言** | TypeScript | 5.x | 类型安全，提升代码质量 |
| **状态管理** | Pinia | 2.x | Vue官方推荐，简单易用 |
| **路由管理** | Vue Router | 4.x | Vue官方路由，功能完善 |
| **样式解决方案** | SCSS + CSS Variables | - | 灵活的样式管理 |
| **图标库** | Element Plus Icons + IconFont | - | 丰富的图标资源 |
| **测试框架** | Vitest | - | Vite原生测试框架 |
| **代码规范** | ESLint + Prettier | - | 代码质量保证 |
| **数据模拟** | Mock.js | - | 开发阶段数据模拟 |

### 用户端技术栈 (cool-admin-uni)

| 技术层 | 技术选择 | 版本 | 理由 |
|--------|----------|------|------|
| **前端框架** | cool-admin-uni | 8.x | 一套代码编译多端应用，兼容小程序、H5等 |
| **构建工具** | HBuilderX | 3.x | uni-app官方IDE，开发调试更便捷 |
| **基础框架** | uni-app | 最新版 | 多端统一开发，支持微信小程序、H5等多端 |
| **UI组件库** | uView | 最新版 | 丰富的组件库，适配多端 |
| **状态管理** | Pinia | 2.x | 轻量级状态管理，支持Vue 3组合式API |
| **样式方案** | SCSS + uView组件样式 | - | 灵活的样式管理 |
| **表单处理** | uni-forms | - | uni-app官方表单组件，支持校验 |
| **数据验证** | uView内置验证 | - | 完整的表单验证方案 |
| **图标库** | uView图标 + IconFont | - | 多端兼容的图标方案 |
| **HTTP请求** | uni.request封装 | - | 统一的请求拦截、响应处理 |
| **路由管理** | uni-app路由 | - | 页面跳转、参数传递 |
| **多端适配** | 条件编译 | - | 针对不同平台编写特定代码 |

### 后端服务技术栈 (cool-admin-midway 8.x)

| 技术层 | 技术选择 | 版本 | 理由 |
|--------|----------|------|------|
| **后端框架** | cool-admin-midway | 8.0.x | 基于Midway.js的企业级开发框架，开发效率高 |
| **基础框架** | Midway.js | 3.20.3+ | 阿里巴巴开源Node.js框架，IoC容器，模块化 |
| **Web框架** | Koa.js | 2.x | 轻量级Web框架，Midway底层使用 |
| **运行时** | Node.js | 18+ | JavaScript统一技术栈 |
| **数据库** | MySQL | 8.0+ | 兼容性好，多项目实践验证 |
| **ORM框架** | TypeORM | 0.3.20 | TypeScript优先，cool-admin-midway默认ORM |
| **缓存** | Redis | 7.x | 高性能，数据结构丰富，通过cache-manager-ioredis-yet实现 |
| **认证** | @cool-midway/core | 8.0.4 | 框架内置认证体系 |
| **文件上传** | @midwayjs/upload | 3.20.3 | Midway官方文件上传组件 |
| **定时任务** | @cool-midway/task | 8.0.2 | Cool内置定时任务模块 |
| **进程管理** | PM2 | - | 生产环境进程管理 |

### 第三方集成

| 技术层 | 技术选择 | 理由 |
|--------|----------|------|
| **微信接入** | @cool-midway/wxpay + 微信SDK | 框架内置微信支付和消息处理模块 |
| **支付集成** | @cool-midway/pay | 统一支付接口，支持微信支付、支付宝等 |
| **任务队列** | @cool-midway/task + Redis | 处理异步任务，如订单超时 |
| **文件上传** | @midwayjs/upload | Midway官方文件上传组件，支持本地和云存储 |
| **参数验证** | @midwayjs/validate | 请求参数自动验证 |
| **日志系统** | @midwayjs/logger | Midway官方日志组件 |
| **API文档** | Swagger/OpenAPI | 自动生成API文档 |
| **部署平台** | Docker | 容器化部署 |
| **监控告警** | Sentry + Prometheus | 错误追踪，性能监控 |
| **CI/CD** | GitHub Actions | 自动化构建和部署 |
| **反向代理** | Nginx | 高性能Web服务器，负载均衡 |
| **SSL证书** | Let's Encrypt | 免费SSL证书，HTTPS支持 |

## Cool-Admin 框架核心特性

### 管理后台(cool-admin-vue 8.x)核心特性
- **🚀 极速启动**：基于 Vite，项目秒级启动，热更新极快
- **📦 丰富模板**：多种布局和主题，覆盖常见业务场景
- **🌐 国际化**：完整的多语言解决方案
- **🎨 主题定制**：灵活的主题配置，支持暗黑模式
- **💡 最佳实践**：内置Vue 3 + Composition API最佳实践
- **📱 响应式设计**：完美适配PC、平板、手机
- **🔧 TypeScript**：全面的TypeScript支持
- **🔐 权限管理**：完整的RBAC权限控制系统
- **🛠️ 开发工具**：完善的开发工具链和调试支持
- **📊 数据可视化**：内置图表组件和数据展示方案

### 后端框架(cool-admin-midway 8.x)核心特性
- **模块化设计**：按业务划分模块，独立开发、部署
- **依赖注入**：基于IoC容器的依赖注入，更好的类型支持
- **装饰器开发**：使用装饰器简化开发流程
- **通用CRUD**：快速实现CRUD接口，减少重复代码
- **权限系统**：完整的RBAC权限管理
- **参数验证**：请求参数自动验证
- **统一响应**：统一的API响应格式
- **文件上传**：内置文件上传功能
- **定时任务**：内置定时任务管理
- **多数据源**：支持多数据源配置
- **缓存管理**：内置缓存管理功能

### 小程序框架(cool-admin-uni)核心特性

- **一次开发，多端适用**：使用cool-admin-uni框架开发后可编译为微信小程序、H5等多个平台应用
- **组件化开发**：内置丰富的UI组件，配合uView提供完整的移动端UI解决方案
- **状态管理**：集成Pinia状态管理，支持Vue 3组合式API
- **路由管理**：统一的路由配置和页面跳转管理
- **请求封装**：统一的网络请求封装，支持请求拦截、响应拦截、错误处理
- **样式主题**：可配置的主题样式，支持深色模式
- **权限管理**：内置权限控制功能，可控制页面访问和按钮操作权限
- **常用工具**：提供常用工具函数，如日期处理、数据校验、本地存储等
- **条件编译**：支持各平台条件编译，解决跨平台差异
- **微信生态集成**：快速接入微信登录、支付、分享等微信能力
- **用户体验优化**：页面转场动画、下拉刷新、上拉加载等交互体验增强

### 多端开发流程

```
开发流程：
├── 环境准备
│   ├── 安装HBuilderX
│   ├── 安装微信开发者工具
│   └── 配置小程序AppId
├── 项目初始化
│   ├── 创建cool-admin-uni项目
│   ├── 配置项目基础信息
│   └── 连接后端API
├── 功能开发
│   ├── 页面开发
│   ├── 组件开发
│   └── API对接
├── 调试测试
│   ├── HBuilderX模拟器调试
│   ├── 真机调试
│   └── 微信开发者工具调试
└── 打包发布
    ├── 小程序端打包
    └── H5端打包部署
```

### 小程序端功能模块

```
小程序端功能模块：
├── 用户模块
│   ├── 微信授权登录
│   ├── 个人中心
│   ├── 我的学习
│   └── 消息通知
├── 内容模块
│   ├── 科目分类
│   ├── 题库练习
│   ├── 错题本
│   └── 收藏夹
├── 学习模块
│   ├── 智能推荐
│   ├── 学习记录
│   ├── 统计分析
│   └── 模拟考试
├── 支付模块
│   ├── 微信支付
│   ├── 订单管理
│   └── 激活码兑换
└── 分销模块
    ├── 分销员申请
    ├── 我的佣金
    └── 分销推广
```

### 内置功能模块
```
管理后台功能模块：
├── 系统管理
│   ├── 用户管理
│   ├── 角色管理
│   ├── 部门管理
│   ├── 菜单管理
│   └── 权限管理
├── 开发管理
│   ├── 数据字典
│   ├── 定时任务
│   ├── 参数配置
│   └── 系统日志
├── 内容管理
│   ├── 科目管理
│   ├── 分类管理
│   ├── 题库管理
│   └── 激活码管理
├── 订单管理
│   ├── 订单列表
│   ├── 支付记录
│   └── 订单统计
├── 用户中心
│   ├── 用户列表
│   ├── 学习记录
│   └── 考试记录
└── 分销管理
    ├── 分销商管理
    ├── 佣金记录
    └── 提现管理
```

## Redis缓存策略

| 缓存类型 | 缓存Key格式 | 缓存内容 | 过期策略 | 使用场景 |
|---------|------------|---------|----------|---------|
| **热门科目** | `subject:hot:[province]` | 热门科目列表 | 1小时 | 首页推荐，减轻数据库压力 |
| **科目详情** | `subject:detail:[id]` | 科目详细信息 | 30分钟 | 科目详情页，高频访问 |
| **题目列表** | `question:list:[subjectId]` | 科目题目列表 | 1小时 | 练习场景，高频访问 |
| **用户权限** | `user:permission:[userId]` | 用户已购科目权限 | 5分钟 | 权限校验，高频访问 |
| **考试试题** | `exam:questions:[examId]` | 考试题目集合 | 24小时 | 考试场景，避免重复组卷 |
| **微信Token** | `wechat:access_token` | 微信接口调用凭证 | 110分钟 | 微信API调用，官方有效期2小时 |

## 核心服务设计

### 用户服务 (modules/user)
- 用户注册登录
- 微信账号绑定
- 用户信息管理
- 权限验证

### 内容服务 (modules/subject & modules/question)
- 科目管理
- 题目管理
- 分类管理
- 本地化显示

### 支付服务 (modules/order)
- 订单管理
- 支付处理
- 激活码管理
- 退款处理

### 微信接入服务 (modules/wechat)
- Token管理
- 消息处理
- 菜单管理
- 模板消息

### 学习服务 (modules/study)
- 学习记录
- 进度跟踪
- 智能推荐
- 考试管理

### 分销服务 (modules/distributor)
- 分销商管理
- 佣金计算
- 提现处理
- 推广统计

## 安全设计

### 数据安全
- 敏感数据加密存储
- API接口鉴权
- SQL注入防护
- XSS攻击防护

### 业务安全
- 支付安全验证
- 考试防作弊
- 分销风控
- 激活码防重复使用

### 系统安全
- HTTPS强制加密
- 访问频率限制
- 异常监控告警
- 数据备份恢复
