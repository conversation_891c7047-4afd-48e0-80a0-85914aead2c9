# 考理论教育平台 - 用户流程文档

## 1. 小程序端用户流程

### 1.1 用户注册与登录流程

```mermaid
flowchart TD
    A[用户打开小程序] --> B{是否有登录态?}
    B -->|是| C[进入首页]
    B -->|否| D[微信授权登录页]
    D --> E{用户是否授权?}
    E -->|是| F[获取微信用户信息]
    E -->|否| G[仅浏览模式]
    F --> H[后端创建或关联用户]
    H --> C
    C --> I[功能菜单]
    I --> J[科目浏览]
    I --> K[个人中心]
    I --> L[学习记录]
```

**登录流程说明:**
1. 用户首次打开小程序时，检查是否有登录态
2. 如无登录态，则引导用户进行微信授权登录
3. 用户授权后，前端获取微信临时code
4. 将code发送至后端，后端通过微信API获取用户openid和unionid
5. 根据openid/unionid查询是否存在用户，不存在则创建新用户
6. 返回用户token及基本信息，前端保存登录态
7. 登录成功后进入小程序首页

**登录态管理:**
- 登录成功后，前端存储token到本地存储
- 每次请求API时携带token进行身份验证
- token过期后自动触发静默登录（使用refresh_token机制）
- 登录态失效时，清除本地token并重新引导登录

### 1.2 学习内容浏览与购买流程

```mermaid
flowchart TD
    A[首页] --> B[科目列表]
    B --> C[科目详情页]
    C --> D{是否已购买?}
    D -->|是| E[进入学习]
    D -->|否| F[查看价格方案]
    F --> G[选择价格方案]
    G --> H[提交订单]
    H --> I[微信支付]
    I --> J{支付成功?}
    J -->|是| K[更新订单状态]
    K --> E
    J -->|否| L[支付失败提示]
    L --> F
    E --> M[题目练习]
    E --> N[模拟考试]
    E --> O[错题收藏]
```

**内容浏览与购买说明:**
1. 用户可在首页浏览科目分类和热门科目
2. 点击科目进入详情页，显示科目介绍、题目数量、用户评价等信息
3. 系统自动判断用户是否已购买该科目:
   - 已购买：直接进入学习界面
   - 未购买：显示价格方案选择界面
4. 用户选择价格方案后，生成订单并调起微信支付
5. 支付成功后，系统自动激活用户的科目权限，计算有效期至validUntil字段
6. 用户随即可进入该科目的学习内容

### 1.3 学习与练习流程

```mermaid
flowchart TD
    A[科目学习页] --> B[选择学习模式]
    B --> C[顺序练习]
    B --> D[随机练习]
    B --> E[错题练习]
    B --> F[收藏练习]
    B --> G[模拟考试]
    C & D & E & F --> H[题目答题界面]
    H --> I[用户作答]
    I --> J[提交答案]
    J --> K[显示结果与解析]
    K --> L{继续答题?}
    L -->|是| M[下一题]
    M --> H
    L -->|否| N[结束练习]
    N --> O[显示成绩统计]
    G --> P[考试模式界面]
    P --> Q[限时作答多题]
    Q --> R[提交试卷]
    R --> S[显示考试结果]
    S --> O
```

**学习流程说明:**
1. 用户进入已购买的科目学习页，可选择不同的学习模式:
   - 顺序练习: 按题库顺序练习
   - 随机练习: 随机抽取题目练习
   - 错题练习: 复习曾经答错的题目
   - 收藏练习: 练习用户收藏的题目
   - 模拟考试: 模拟真实考试场景
2. 练习模式下:
   - 每道题作答后立即显示正误及解析
   - 可标记收藏或疑难题目
   - 可随时暂停或结束练习
3. 考试模式下:
   - 限定时间内完成所有题目
   - 提交前不显示答案和解析
   - 提交后一次性展示所有结果
4. 学习记录自动保存，包括:
   - 正确率统计
   - 做题时间分析
   - 错题集自动收集
   - 学习进度跟踪

### 1.4 分销与推广流程

```mermaid
flowchart TD
    A[个人中心] --> B[申请分销商]
    B --> C{是否符合条件?}
    C -->|是| D[填写分销商信息]
    C -->|否| E[显示申请条件]
    D --> F[提交审核]
    F --> G{审核结果}
    G -->|通过| H[成为分销商]
    G -->|拒绝| I[显示拒绝原因]
    H --> J[获取推广码/链接]
    J --> K[分享给好友]
    K --> L[好友下单]
    L --> M[确认订单]
    M --> N[结算佣金]
    N --> O[分销商收益]
```

**分销流程说明:**
1. 用户在个人中心申请成为分销商:
   - 需满足特定条件(如已购买至少一个科目)
   - 填写必要的分销商信息
2. 系统或管理员审核分销商申请
3. 成为分销商后，可获取专属推广码或链接
4. 推广方式:
   - 分享小程序码给好友
   - 生成专属海报分享朋友圈
   - 直接转发科目详情页
5. 通过分销渠道购买的用户，订单会自动关联分销商ID
6. 佣金规则:
   - 一级分销商获得订单金额的固定比例佣金
   - 佣金在用户支付成功后计入分销商账户
   - 佣金可在达到提现门槛后申请提现

### 1.5 订单与支付流程

```mermaid
flowchart TD
    A[科目详情页] --> B[选择价格方案]
    B --> C[生成订单]
    C --> D{选择支付方式}
    D -->|微信支付| E[调起微信支付]
    D -->|激活码| F[输入激活码]
    E --> G{支付结果}
    F --> H{激活码验证}
    G -->|成功| I[支付成功页面]
    G -->|失败| J[支付失败提示]
    H -->|有效| I
    H -->|无效| K[激活码错误提示]
    I --> L[更新订单状态]
    L --> M[授予科目访问权限]
    M --> N[进入学习页面]
    J --> O[重新支付]
    O --> D
    K --> P[重新输入激活码]
    P --> F
```

**支付流程说明:**
1. 用户选择购买科目后，选择价格方案(如30天、90天、永久等)
2. 系统生成唯一订单号，创建未支付状态的订单记录
3. 用户可选择支付方式:
   - 微信支付: 调起微信支付接口
   - 激活码: 通过课程激活码激活
4. 微信支付流程:
   - 前端调用统一下单接口，后端与微信支付系统交互
   - 用户确认支付金额并完成支付
   - 微信支付平台回调通知支付结果
   - 系统验证支付结果有效性并更新订单状态
5. 激活码流程:
   - 用户输入获取到的激活码
   - 系统验证激活码的有效性(是否存在、是否使用过、是否过期)
   - 有效则标记订单为已支付状态，并标记激活码为已使用
6. 支付成功后:
   - 更新用户权限，设置科目访问有效期
   - 如有分销商信息，记录分销关系并计算佣金
   - 引导用户进入科目学习页面

## 2. 管理后台用户流程

### 内容管理流程

```mermaid
graph TD
    A[进入内容管理] --> B{选择管理类型}
    B -->|科目管理| C[科目列表页面]
    B -->|题库管理| D[题库列表页面]
    B -->|分类管理| E[分类树形结构]

    C --> F{科目操作}
    F -->|新增| G[填写科目信息]
    F -->|编辑| H[修改科目信息]
    F -->|删除| I[确认删除操作]
    F -->|上下架| J[切换科目状态]

    G --> K[设置价格方案]
    K --> L[配置显示模板]
    L --> M[保存科目信息]

    H --> N[更新科目信息]
    N --> O[记录操作日志]

    I --> P{确认删除}
    P -->|是| Q[检查关联数据]
    P -->|否| C
    Q --> R{有关联数据}
    R -->|是| S[提示无法删除]
    R -->|否| T[执行删除]

    D --> U{题目操作}
    U -->|批量导入| V[上传Excel文件]
    U -->|单个新增| W[填写题目信息]
    U -->|编辑题目| X[修改题目内容]
    U -->|删除题目| Y[确认删除]

    V --> Z[验证文件格式]
    Z --> AA{格式正确}
    AA -->|是| BB[解析题目数据]
    AA -->|否| CC[显示格式错误]

    BB --> DD[批量保存题目]
    DD --> EE[更新科目题目数量]

    W --> FF[添加题目选项]
    FF --> GG[设置正确答案]
    GG --> HH[保存题目]

    M --> O
    T --> O
    HH --> O
    EE --> O
    O --> II[刷新页面数据]
```

### 订单管理流程

```mermaid
graph TD
    A[进入订单管理] --> B[显示订单列表]
    B --> C{订单筛选}
    C -->|全部订单| D[显示所有订单]
    C -->|待支付| E[显示未支付订单]
    C -->|已支付| F[显示已支付订单]
    C -->|异常订单| G[显示异常订单]

    D --> H{订单操作}
    E --> H
    F --> H
    G --> H

    H -->|查看详情| I[显示订单详细信息]
    H -->|处理异常| J[异常订单处理]
    H -->|手动开通| K[手动开通权限]
    H -->|取消订单| L[取消订单操作]

    I --> M[查看支付记录]
    M --> N[查看用户信息]
    N --> O[查看科目信息]

    J --> P{异常类型}
    P -->|支付成功未开通| Q[检查支付状态]
    P -->|重复支付| R[处理重复支付]
    P -->|金额异常| S[核实支付金额]

    Q --> T{支付确实成功}
    T -->|是| U[手动开通权限]
    T -->|否| V[联系支付平台]

    R --> W[退还多余款项]
    W --> X[保留一个有效订单]

    S --> Y[人工核实]
    Y --> Z{金额正确}
    Z -->|是| U
    Z -->|否| AA[联系用户确认]

    K --> BB[选择开通科目]
    BB --> CC[设置有效期]
    CC --> DD[确认开通]
    DD --> EE[更新订单状态]
    EE --> FF[发送开通通知]

    L --> GG{确认取消}
    GG -->|是| HH[取消订单]
    GG -->|否| B
    HH --> II[更新订单状态]
    II --> JJ[发送取消通知]

    U --> KK[记录处理日志]
    FF --> KK
    JJ --> KK
    KK --> LL[刷新订单列表]
```

## 异常处理流程

### 支付异常处理
1. **支付超时**：30分钟后自动取消订单
2. **支付失败**：提示用户重新支付或更换支付方式
3. **重复支付**：自动退款或转为余额
4. **金额异常**：人工审核处理

### 考试异常处理
1. **网络中断**：本地缓存答案，恢复后同步
2. **应用切换**：记录次数，超限强制交卷
3. **系统故障**：保存当前进度，恢复后继续
4. **时间异常**：服务器时间为准，客户端时间仅参考

### 数据异常处理
1. **数据丢失**：从备份恢复
2. **数据不一致**：数据校验和修复
3. **并发冲突**：乐观锁处理
4. **系统错误**：错误日志记录和告警
