# 考理论教育平台 - 数据模型文档

## 核心实体关系

```
用户(User) ──┬── 订单(Order)
            ├── 学习记录(StudyRecord)
            ├── 考试记录(ExamRecord)
            ├── 错题记录(WrongQuestion)
            ├── 收藏题目(FavoriteQuestion)
            └── 分销商(Distributor)

科目(Subject) ──┬── 题目(Question)
               ├── 考试(Exam)
               ├── 价格方案(SubjectPrice)
               └── 订单(Order)

题目(Question) ──┬── 选项(QuestionOption)
                ├── 错题记录(WrongQuestion)
                └── 收藏记录(FavoriteQuestion)
```

## 用户体系模型

### 用户基础信息 (User Entity)

**实体说明**: 存储平台用户的基本信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      | 用户唯一标识 (建议使用UUID或CUID) |
| `nickname`           | `string`               | `true`          | `false`            | `nickname`                | `varchar(50)`      | 昵称           |
| `avatar`             | `string`               | `true`          | `false`            | `avatar`                  | `varchar(500)`     | 头像URL        |
| `phone`              | `string`               | `true`          | `false`            | `phone`                   | `varchar(20)`      | 手机号码 (唯一) |
| `city`               | `string`               | `true`          | `false`            | `city`                    | `varchar(50)`      | 城市           |
| `province`           | `string`               | `true`          | `false`            | `province`                | `varchar(50)`      | 省份           |
| `status`             | `number`               | `false`         | `false`            | `status`                  | `smallint`         | 状态 (1-正常 2-禁用)，默认1 |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         | 创建时间       |
| `updatedAt`          | `Date`                 | `false`         | `false`            | `updated_at`              | `datetime`         | 更新时间       |

**移除的 Prisma 关系字段**:
* `wechatAccounts: WechatAccount[]`
* `orders: Order[]`
* `studyRecords: StudyRecord[]`
* `examRecords: ExamRecord[]`
* `wrongQuestions: WrongQuestion[]`
* `favoriteQuestions: FavoriteQuestion[]`
* `distributor: Distributor?`

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 微信账号信息 (WechatAccount Entity)

**实体说明**: 存储用户绑定的微信账号信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      | 唯一标识 (建议使用UUID或CUID) |
| `userId`             | `string`               | `false`         | `false`            | `user_id`                 | `varchar(36)`      | 关联的用户ID   |
| `unionId`            | `string`               | `true`          | `false`            | `union_id`                | `varchar(100)`     | 微信 UnionID (唯一) |
| `openId`             | `string`               | `false`         | `false`            | `open_id`                 | `varchar(100)`     | 微信 OpenID (唯一) |
| `platform`           | `number`               | `false`         | `false`            | `platform`                | `smallint`         | 平台 (1-小程序 2-公众号) |
| `nickname`           | `string`               | `true`          | `false`            | `nickname`                | `varchar(50)`      | 微信昵称       |
| `avatar`             | `string`               | `true`          | `false`            | `avatar`                  | `varchar(500)`     | 微信头像URL    |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         | 创建时间       |

**移除的 Prisma 关系字段**:
* `user: User` (通过 `userId` 关联)

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 分销商信息 (Distributor Entity)

**实体说明**: 存储分销商的详细信息，包括层级、佣金、收益等。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 分销商唯一标识 (建议使用UUID或CUID) |
| `userId`             | `string`               | `false`         | `false`            | `user_id`                 | `varchar(36)`      |                       |                        | 关联的用户ID (唯一) |
| `parentId`           | `string`               | `true`          | `false`            | `parent_id`               | `varchar(36)`      |                       |                        | 上级分销商ID   |
| `level`              | `number`               | `false`         | `false`            | `level`                   | `smallint`         | `1`                   |                        | 分销层级       |
| `commission`         | `number`               | `false`         | `false`            | `commission`              | `decimal(5,4)`     | `0.15`                |                        | 佣金比例 (例如0.15代表15%) |
| `totalEarned`        | `number`               | `false`         | `false`            | `total_earned`            | `decimal(10,2)`    | `0`                   |                        | 累计收益       |
| `balance`            | `number`               | `false`         | `false`            | `balance`                 | `decimal(10,2)`    | `0`                   |                        | 可提现余额     |
| `status`             | `number`               | `false`         | `false`            | `status`                  | `smallint`         | `0`                   | `dict: ["正常", "禁用"]` | 状态 (0-正常, 1-禁用) |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间       |
| `updatedAt`          | `Date`                 | `false`         | `false`            | `updated_at`              | `datetime`         |                       |                        | 更新时间       |

**注意**: 
* `status` 字段：Prisma 原定义为 `Int @default(1) // 1-正常 2-禁用`。根据 cool-admin v8.x `dict` 数组的0索引约定，这里调整为 `dict: ["正常", "禁用"]`，数据库中存储 `0` 代表 "正常"，`1` 代表 "禁用"，默认值为 `0` ("正常")。
* `userId` 字段在 Prisma 中有 `@unique` 约束。
* `commission`, `totalEarned`, `balance` 是 `Decimal` 类型，在TypeORM中通常映射为 `number` 或 `string`，这里暂定为 `number`，具体精度依赖数据库列类型。

**移除的 Prisma 关系字段**:
* `user: User` (通过 `userId` 关联)
* `parent: Distributor?` (通过 `parentId` 关联)
* `children: Distributor[]`
* `withdrawRecords: WithdrawRecord[]`

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

## 内容体系模型

### 分类信息 (Category Entity)

**实体说明**: 存储内容分类的层级结构信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 分类唯一标识 |
| `name`               | `string`               | `false`         | `false`            | `name`                    | `varchar(100)`     |                       |                        | 分类名称 |
| `parentId`           | `string`               | `true`          | `false`            | `parent_id`               | `varchar(36)`      |                       |                        | 父分类ID |
| `level`              | `number`               | `false`         | `false`            | `level`                   | `smallint`         | `1`                   |                        | 层级 |
| `orderNum`           | `number`               | `false`         | `false`            | `order_num`               | `int`              | `0`                   |                        | 排序号 |
| `status`             | `number`               | `false`         | `false`            | `status`                  | `smallint`         | `0`                   | `dict: ["正常", "禁用"]` | 状态 (0-正常, 1-禁用) |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间 |

**注意**: 
* `status` 字段：Prisma 原定义为 `Int @default(1) @db.SmallInt`。根据 cool-admin v8.x `dict` 数组的0索引约定，这里调整为 `dict: ["正常", "禁用"]`，数据库中存储 `0` 代表 "正常"，`1` 代表 "禁用"，默认值为 `0` ("正常")。

**移除的 Prisma 关系字段**:
* `parent: Category?` (通过 `parentId` 关联)
* `children: Category[]`
* `subjects: Subject[]`

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 科目信息 (Subject Entity)

**实体说明**: 存储教育平台的科目信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 科目唯一标识 |
| `name`               | `string`               | `false`         | `false`            | `name`                    | `varchar(200)`     |                       |                        | 科目名称 |
| `description`        | `string`               | `true`          | `false`            | `description`             | `text`             |                       |                        | 科目描述 |
| `categoryId`         | `string`               | `false`         | `false`            | `category_id`             | `varchar(36)`      |                       |                        | 分类ID |
| `displayTemplate`    | `string`               | `true`          | `false`            | `display_template`        | `varchar(200)`     |                       |                        | 显示模板 |
| `questionCount`      | `number`               | `false`         | `false`            | `question_count`          | `int`              | `0`                   |                        | 题目数量 |
| `orderNum`           | `number`               | `false`         | `false`            | `order_num`               | `int`              | `0`                   |                        | 排序号 |
| `status`             | `number`               | `false`         | `false`            | `status`                  | `smallint`         | `0`                   | `dict: ["上架", "下架"]` | 状态 (0-上架, 1-下架) |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间 |
| `updatedAt`          | `Date`                 | `false`         | `false`            | `updated_at`              | `datetime`         |                       |                        | 更新时间 |

**注意**: 
* `status` 字段：Prisma 原定义为 `Int @default(1) @db.SmallInt // 1-上架 2-下架`。根据 cool-admin v8.x 的约定，这里调整为 `dict: ["上架", "下架"]`，数据库中存储 `0` 代表 "上架"，`1` 代表 "下架"，默认值为 `0` ("上架")。

**移除的 Prisma 关系字段**:
* `category: Category` (通过 `categoryId` 关联)
* `questions: Question[]`
* `prices: SubjectPrice[]`
* `orders: Order[]`
* `studyRecords: StudyRecord[]`
* `examRecords: ExamRecord[]`

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 科目价格方案
```prisma
model SubjectPrice {
  id        String   @id @default(cuid())
  subjectId String   @map("subject_id")
  name      String   @db.VarChar(100) // 套餐名称
  price     Decimal  @db.Decimal(10, 2)
  validDays Int      @map("valid_days") // 有效天数
  isDefault Boolean  @default(false) @map("is_default")
  status    Int      @default(1) @db.SmallInt
  createdAt DateTime @default(now()) @map("created_at")

  // 关系
  subject   Subject  @relation(fields: [subjectId], references: [id])
  orders    Order[]

  @@map("subject_prices")
}
```

### 题目信息
```prisma
model Question {
  id        String   @id @default(cuid())
  subjectId String   @map("subject_id")
  type      Int      @db.SmallInt // 1-单选 2-多选 3-判断
  content   String   @db.Text
  analysis  String?  @db.Text // 解析
  difficulty Int     @default(1) @db.SmallInt // 1-简单 2-中等 3-困难
  orderNum  Int      @default(0) @map("order_num")
  status    Int      @default(1) @db.SmallInt
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关系
  subject           Subject            @relation(fields: [subjectId], references: [id])
  options           QuestionOption[]
  studyRecords      StudyRecord[]
  wrongQuestions    WrongQuestion[]
  favoriteQuestions FavoriteQuestion[]

  @@map("questions")
}
```

### 题目选项
```prisma
model QuestionOption {
  id         String   @id @default(cuid())
  questionId String   @map("question_id")
  content    String   @db.Text
  isCorrect  Boolean  @default(false) @map("is_correct")
  orderNum   Int      @default(0) @map("order_num")
  createdAt  DateTime @default(now()) @map("created_at")

  // 关系
  question   Question @relation(fields: [questionId], references: [id])

  @@map("question_options")
}
```

## 交易体系模型

### 订单信息
```prisma
model Order {
  id          String   @id @default(cuid())
  orderNo     String   @unique @map("order_no") @db.VarChar(32)
  userId      String   @map("user_id")
  subjectId   String   @map("subject_id")
  priceId     String   @map("price_id")
  amount      Decimal  @db.Decimal(10, 2)
  payAmount   Decimal  @default(0) @map("pay_amount") @db.Decimal(10, 2)
  status      Int      @default(0) @db.SmallInt // 0-待支付 1-已支付 2-已取消 3-异常
  validUntil  DateTime? @map("valid_until") // 权限有效期
  distributorId String? @map("distributor_id") // 推广人
  paidAt      DateTime? @map("paid_at")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关系
  user        User           @relation(fields: [userId], references: [id])
  subject     Subject        @relation(fields: [subjectId], references: [id])
  price       SubjectPrice   @relation(fields: [priceId], references: [id])
  payments    PaymentRecord[]

  @@map("orders")
}
```

### 支付记录
```prisma
model PaymentRecord {
  id           String   @id @default(cuid())
  orderId      String   @map("order_id")
  paymentType  Int      @db.SmallInt // 1-微信支付 2-支付宝支付 3-激活码
  amount       Decimal  @db.Decimal(10, 2)
  tradeNo      String?  @map("trade_no") @db.VarChar(100)
  thirdTradeNo String?  @map("third_trade_no") @db.VarChar(100)
  activationCode String? @map("activation_code") @db.VarChar(50)
  status       Int      @default(0) @db.SmallInt // 0-待支付 1-支付成功 2-支付失败
  paidAt       DateTime? @map("paid_at")
  failReason   String?  @map("fail_reason") @db.Text
  createdAt    DateTime @default(now()) @map("created_at")

  // 关系
  order        Order    @relation(fields: [orderId], references: [id])

  @@map("payment_records")
}
```

### 激活码管理
```prisma
model ActivationCode {
  id        String   @id @default(cuid())
  code      String   @unique @db.VarChar(20)
  subjectId String   @map("subject_id")
  validDays Int      @map("valid_days")
  usedBy    String?  @map("used_by") // 使用者用户ID
  usedAt    DateTime? @map("used_at")
  status    Int      @default(0) @db.SmallInt // 0-未使用 1-已使用 2-已过期 3-已禁用
  createdAt DateTime @default(now()) @map("created_at")
  expiredAt DateTime? @map("expired_at")

  @@map("activation_codes")
}
```

## 学习体系模型

### 学习记录
```prisma
model StudyRecord {
  id          String   @id @default(cuid())
  userId      String   @map("user_id")
  subjectId   String   @map("subject_id")
  questionId  String   @map("question_id")
  practiceType Int     @db.SmallInt // 1-顺序 2-题型 3-错题 4-随机 5-收藏 6-搜题
  userAnswer  String?  @map("user_answer") @db.Text // 用户答案
  isCorrect   Boolean? @map("is_correct")
  timeSpent   Int      @default(0) @map("time_spent") // 答题耗时(秒)
  createdAt   DateTime @default(now()) @map("created_at")

  // 关系
  user        User     @relation(fields: [userId], references: [id])
  subject     Subject  @relation(fields: [subjectId], references: [id])
  question    Question @relation(fields: [questionId], references: [id])

  @@unique([userId, questionId])
  @@map("study_records")
}
```

### 考试记录
```prisma
model ExamRecord {
  id          String   @id @default(cuid())
  userId      String   @map("user_id")
  subjectId   String   @map("subject_id")
  score       Int      @default(0) // 考试得分
  totalScore  Int      @map("total_score") // 总分
  passScore   Int      @map("pass_score") // 及格分
  isPassed    Boolean  @default(false) @map("is_passed")
  timeSpent   Int      @map("time_spent") // 考试耗时(秒)
  answers     Json     // 答题详情
  startedAt   DateTime @map("started_at")
  finishedAt  DateTime? @map("finished_at")
  createdAt   DateTime @default(now()) @map("created_at")

  // 关系
  user        User     @relation(fields: [userId], references: [id])
  subject     Subject  @relation(fields: [subjectId], references: [id])

  @@map("exam_records")
}
```

### 错题记录
```prisma
model WrongQuestion {
  id          String   @id @default(cuid())
  userId      String   @map("user_id")
  questionId  String   @map("question_id")
  wrongCount  Int      @default(1) @map("wrong_count") // 错误次数
  lastWrongAt DateTime @map("last_wrong_at")
  isMastered  Boolean  @default(false) @map("is_mastered") // 是否已掌握
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关系
  user        User     @relation(fields: [userId], references: [id])
  question    Question @relation(fields: [questionId], references: [id])

  @@unique([userId, questionId])
  @@map("wrong_questions")
}
```

### 收藏题目
```prisma
model FavoriteQuestion {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  questionId String  @map("question_id")
  note      String?  @db.Text // 个人备注
  createdAt DateTime @default(now()) @map("created_at")

  // 关系
  user      User     @relation(fields: [userId], references: [id])
  question  Question @relation(fields: [questionId], references: [id])

  @@unique([userId, questionId])
  @@map("favorite_questions")
}
```

## 微信体系模型

### 微信消息记录
```prisma
model WechatMessage {
  id        String   @id @default(cuid())
  fromUser  String   @map("from_user") @db.VarChar(100)    // 发送方OpenID
  toUser    String   @map("to_user") @db.VarChar(100)      // 接收方OpenID
  msgType   String   @map("msg_type") @db.VarChar(20)      // 消息类型
  content   String?  @db.Text                              // 消息内容
  mediaId   String?  @map("media_id") @db.VarChar(100)     // 媒体文件ID
  event     String?  @db.VarChar(50)                       // 事件类型
  eventKey  String?  @map("event_key") @db.VarChar(100)    // 事件KEY
  msgId     String?  @map("msg_id") @db.VarChar(100)       // 消息ID
  createdAt DateTime @default(now()) @map("created_at")

  @@map("wechat_messages")
}
```

### 微信菜单配置
```prisma
model WechatMenu {
  id        String   @id @default(cuid())
  name      String   @db.VarChar(50)                       // 菜单名称
  type      String   @db.VarChar(20)                       // 菜单类型
  key       String?  @db.VarChar(100)                      // 菜单KEY
  url       String?  @db.VarChar(500)                      // 跳转URL
  appId     String?  @map("app_id") @db.VarChar(100)       // 小程序AppID
  pagePath  String?  @map("page_path") @db.VarChar(200)    // 小程序页面路径
  parentId  String?  @map("parent_id")                     // 父菜单ID
  orderNum  Int      @default(0) @map("order_num")         // 排序号
  status    Int      @default(1) @db.SmallInt              // 状态
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关系
  parent    WechatMenu?  @relation("MenuHierarchy", fields: [parentId], references: [id])
  children  WechatMenu[] @relation("MenuHierarchy")

  @@map("wechat_menus")
}
```

## 管理体系模型

### 管理员用户
```prisma
model AdminUser {
  id          String    @id @default(cuid())
  username    String    @unique @db.VarChar(50)        // 用户名
  password    String    @db.VarChar(255)               // 加密密码
  realName    String    @map("real_name") @db.VarChar(50)  // 真实姓名
  email       String?   @unique @db.VarChar(100)       // 邮箱
  phone       String?   @db.VarChar(20)                // 手机号
  avatar      String?   @db.VarChar(500)               // 头像
  role        String    @default("admin") @db.VarChar(20)  // 角色
  status      Int       @default(1) @db.SmallInt       // 状态：1-正常 2-禁用
  lastLogin   DateTime? @map("last_login")             // 最后登录时间
  loginCount  Int       @default(0) @map("login_count") // 登录次数
  createdBy   String?   @map("created_by")             // 创建人
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // 关系
  createdLogs OperationLog[] @relation("CreatedLogs")

  @@map("admin_users")
}
```

### 系统配置
```prisma
model SystemConfig {
  id          String   @id @default(cuid())
  key         String   @unique @db.VarChar(100)        // 配置键
  value       String   @db.Text                        // 配置值
  type        String   @default("string") @db.VarChar(20)  // 数据类型
  group       String   @default("system") @db.VarChar(50)  // 配置分组
  name        String   @db.VarChar(100)                // 配置名称
  description String?  @db.VarChar(500)                // 配置描述
  isPublic    Boolean  @default(false) @map("is_public")   // 是否公开
  isRequired  Boolean  @default(false) @map("is_required") // 是否必需
  sortOrder   Int      @default(0) @map("sort_order")  // 排序
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("system_configs")
}
```

### 操作日志
```prisma
model OperationLog {
  id        String   @id @default(cuid())
  adminId   String?  @map("admin_id")                  // 操作管理员ID
  userType  String   @map("user_type") @db.VarChar(20) // 用户类型：admin, user
  action    String   @db.VarChar(100)                  // 操作类型
  module    String   @db.VarChar(50)                   // 模块名称
  target    String?  @db.VarChar(100)                  // 操作目标
  content   String?  @db.Text                          // 操作内容
  ip        String?  @db.VarChar(50)                   // IP地址
  userAgent String?  @map("user_agent") @db.Text       // 用户代理
  result    String   @default("success") @db.VarChar(20) // 操作结果
  errorMsg  String?  @map("error_msg") @db.Text        // 错误信息
  createdAt DateTime @default(now()) @map("created_at")

  // 关系
  admin     AdminUser? @relation("CreatedLogs", fields: [adminId], references: [id])

  @@map("operation_logs")
}
```

### 佣金提现记录
```prisma
model WithdrawRecord {
  id            String   @id @default(cuid())
  distributorId String   @map("distributor_id")
  amount        Decimal  @db.Decimal(10, 2)
  fee           Decimal  @default(0) @db.Decimal(10, 2) // 手续费
  actualAmount  Decimal  @map("actual_amount") @db.Decimal(10, 2) // 实际到账金额
  withdrawType  Int      @map("withdraw_type") @db.SmallInt // 1-支付宝 2-微信 3-银行卡
  account       String   @db.VarChar(100) // 提现账号
  accountName   String   @map("account_name") @db.VarChar(50) // 账户名
  status        Int      @default(0) @db.SmallInt // 0-待审核 1-已通过 2-已拒绝 3-已到账
  remark        String?  @db.Text // 备注
  reviewedBy    String?  @map("reviewed_by") // 审核人
  reviewedAt    DateTime? @map("reviewed_at")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // 关系
  distributor   Distributor @relation(fields: [distributorId], references: [id])

  @@map("withdraw_records")
}
```
