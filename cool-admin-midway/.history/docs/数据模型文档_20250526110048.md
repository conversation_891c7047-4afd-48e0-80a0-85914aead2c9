# 考理论教育平台 - 数据模型文档

## 核心实体关系

基于 Cool-Admin 8.x 和 TypeORM 0.3.20 设计的无外键关系数据模型:

```
用户(User) ──┬── 订单(Order)
            ├── 学习记录(StudyRecord)
            ├── 考试记录(ExamRecord)
            ├── 错题记录(WrongQuestion)
            ├── 收藏题目(FavoriteQuestion)
            └── 分销商(Distributor)

科目(Subject) ──┬── 题目(Question)
               ├── 考试(Exam)
               ├── 价格方案(SubjectPrice)
               └── 订单(Order)

题目(Question) ──┬── 选项(QuestionOption)
                ├── 错题记录(WrongQuestion)
                └── 收藏记录(FavoriteQuestion)
```

## TypeORM 实体说明

所有实体均继承自 `BaseEntity`，位于 `src/modules/base/entity/base.ts`。BaseEntity 已经包含以下字段：
- id: 主键，自增
- createTime: 创建时间
- updateTime: 更新时间

数据库设计遵循 Cool-Admin 8.x 规范：
- 不使用外键关系，如 @ManyToOne、@OneToMany 等
- 使用 dict 属性定义枚举值
- 实体字段使用驼峰命名
- TypeORM 版本 0.3.20

## 用户体系模型

### 用户基础信息 (User Entity)

**实体说明**: 存储平台用户的基本信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `nickname` | `string` | `true` | `普通索引` | `nickname` | `varchar(50)` | | | 昵称 |
| `avatar` | `string` | `true` | | `avatar` | `varchar(500)` | | | 头像URL |
| `phone` | `string` | `true` | `唯一索引` | `phone` | `varchar(20)` | | | 手机号码 |
| `city` | `string` | `true` | | `city` | `varchar(50)` | | | 城市 |
| `province` | `string` | `true` | | `province` | `varchar(50)` | | | 省份 |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['正常', '禁用']` | 状态 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |
| `updateTime` | `Date` | `false` | | `updateTime` | `datetime` | 当前时间 | | 更新时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 用户基础信息
 */
@Entity('user')
export class UserEntity extends BaseEntity {
  @Index()
  @Column({ comment: '昵称', nullable: true, length: 50 })
  nickname: string;

  @Column({ comment: '头像URL', nullable: true, length: 500 })
  avatar: string;

  @Index({ unique: true })
  @Column({ comment: '手机号码', nullable: true, length: 20 })
  phone: string;

  @Column({ comment: '城市', nullable: true, length: 50 })
  city: string;

  @Column({ comment: '省份', nullable: true, length: 50 })
  province: string;

  @Column({ 
    comment: '状态', 
    default: 0,
    type: 'tinyint',
    dict: ['正常', '禁用']
  })
  status: number;
}
```

**移除的 Prisma 关系字段**:
* `wechatAccounts: WechatAccount[]`
* `orders: Order[]`
* `studyRecords: StudyRecord[]`
* `examRecords: ExamRecord[]`
* `wrongQuestions: WrongQuestion[]`
* `favoriteQuestions: FavoriteQuestion[]`
* `distributor: Distributor?`

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 微信账号信息 (WechatAccount Entity)

**实体说明**: 存储用户绑定的微信账号信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      | 唯一标识 (建议使用UUID或CUID) |
| `userId`             | `string`               | `false`         | `false`            | `user_id`                 | `varchar(36)`      | 关联的用户ID   |
| `unionId`            | `string`               | `true`          | `false`            | `union_id`                | `varchar(100)`     | 微信 UnionID (唯一) |
| `openId`             | `string`               | `false`         | `false`            | `open_id`                 | `varchar(100)`     | 微信 OpenID (唯一) |
| `platform`           | `number`               | `false`         | `false`            | `platform`                | `smallint`         | 平台 (1-小程序 2-公众号) |
| `nickname`           | `string`               | `true`          | `false`            | `nickname`                | `varchar(50)`      | 微信昵称       |
| `avatar`             | `string`               | `true`          | `false`            | `avatar`                  | `varchar(500)`     | 微信头像URL    |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         | 创建时间       |

**移除的 Prisma 关系字段**:
* `user: User` (通过 `userId` 关联)

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 分销商信息 (Distributor Entity)

**实体说明**: 存储分销商的详细信息，包括层级、佣金、收益等。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 分销商唯一标识 (建议使用UUID或CUID) |
| `userId`             | `string`               | `false`         | `false`            | `user_id`                 | `varchar(36)`      |                       |                        | 关联的用户ID (唯一) |
| `parentId`           | `string`               | `true`          | `false`            | `parent_id`               | `varchar(36)`      |                       |                        | 上级分销商ID   |
| `level`              | `number`               | `false`         | `false`            | `level`                   | `smallint`         | `1`                   |                        | 分销层级       |
| `commission`         | `number`               | `false`         | `false`            | `commission`              | `decimal(5,4)`     | `0.15`                |                        | 佣金比例 (例如0.15代表15%) |
| `totalEarned`        | `number`               | `false`         | `false`            | `total_earned`            | `decimal(10,2)`    | `0`                   |                        | 累计收益       |
| `balance`            | `number`               | `false`         | `false`            | `balance`                 | `decimal(10,2)`    | `0`                   |                        | 可提现余额     |
| `status`             | `number`               | `false`         | `false`            | `status`                  | `smallint`         | `0`                   | `dict: ["正常", "禁用"]` | 状态 (0-正常, 1-禁用) |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间       |
| `updatedAt`          | `Date`                 | `false`         | `false`            | `updated_at`              | `datetime`         |                       |                        | 更新时间       |

**注意**: 
* `status` 字段：Prisma 原定义为 `Int @default(1) // 1-正常 2-禁用`。根据 cool-admin v8.x `dict` 数组的0索引约定，这里调整为 `dict: ["正常", "禁用"]`，数据库中存储 `0` 代表 "正常"，`1` 代表 "禁用"，默认值为 `0` ("正常")。
* `userId` 字段在 Prisma 中有 `@unique` 约束。
* `commission`, `totalEarned`, `balance` 是 `Decimal` 类型，在TypeORM中通常映射为 `number` 或 `string`，这里暂定为 `number`，具体精度依赖数据库列类型。

**移除的 Prisma 关系字段**:
* `user: User` (通过 `userId` 关联)
* `parent: Distributor?` (通过 `parentId` 关联)
* `children: Distributor[]`
* `withdrawRecords: WithdrawRecord[]`

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

## 内容体系模型

### 分类信息 (Category Entity)

**实体说明**: 存储内容分类的层级结构信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 分类唯一标识 |
| `name`               | `string`               | `false`         | `false`            | `name`                    | `varchar(100)`     |                       |                        | 分类名称 |
| `parentId`           | `string`               | `true`          | `false`            | `parent_id`               | `varchar(36)`      |                       |                        | 父分类ID |
| `level`              | `number`               | `false`         | `false`            | `level`                   | `smallint`         | `1`                   |                        | 层级 |
| `orderNum`           | `number`               | `false`         | `false`            | `order_num`               | `int`              | `0`                   |                        | 排序号 |
| `status`             | `number`               | `false`         | `false`            | `status`                  | `smallint`         | `0`                   | `dict: ["正常", "禁用"]` | 状态 (0-正常, 1-禁用) |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间 |

**注意**: 
* `status` 字段：Prisma 原定义为 `Int @default(1) @db.SmallInt`。根据 cool-admin v8.x `dict` 数组的0索引约定，这里调整为 `dict: ["正常", "禁用"]`，数据库中存储 `0` 代表 "正常"，`1` 代表 "禁用"，默认值为 `0` ("正常")。

**移除的 Prisma 关系字段**:
* `parent: Category?` (通过 `parentId` 关联)
* `children: Category[]`
* `subjects: Subject[]`

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 科目信息 (Subject Entity)

**实体说明**: 存储教育平台的科目信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 科目唯一标识 |
| `name`               | `string`               | `false`         | `false`            | `name`                    | `varchar(200)`     |                       |                        | 科目名称 |
| `description`        | `string`               | `true`          | `false`            | `description`             | `text`             |                       |                        | 科目描述 |
| `categoryId`         | `string`               | `false`         | `false`            | `category_id`             | `varchar(36)`      |                       |                        | 分类ID |
| `displayTemplate`    | `string`               | `true`          | `false`            | `display_template`        | `varchar(200)`     |                       |                        | 显示模板 |
| `questionCount`      | `number`               | `false`         | `false`            | `question_count`          | `int`              | `0`                   |                        | 题目数量 |
| `orderNum`           | `number`               | `false`         | `false`            | `order_num`               | `int`              | `0`                   |                        | 排序号 |
| `status`             | `number`               | `false`         | `false`            | `status`                  | `smallint`         | `0`                   | `dict: ["上架", "下架"]` | 状态 (0-上架, 1-下架) |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间 |
| `updatedAt`          | `Date`                 | `false`         | `false`            | `updated_at`              | `datetime`         |                       |                        | 更新时间 |

**注意**: 
* `status` 字段：Prisma 原定义为 `Int @default(1) @db.SmallInt // 1-上架 2-下架`。根据 cool-admin v8.x 的约定，这里调整为 `dict: ["上架", "下架"]`，数据库中存储 `0` 代表 "上架"，`1` 代表 "下架"，默认值为 `0` ("上架")。

**移除的 Prisma 关系字段**:
* `category: Category` (通过 `categoryId` 关联)
* `questions: Question[]`
* `prices: SubjectPrice[]`
* `orders: Order[]`
* `studyRecords: StudyRecord[]`
* `examRecords: ExamRecord[]`

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 科目价格方案 (SubjectPrice Entity)

**实体说明**: 存储科目的不同价格套餐方案。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 价格方案唯一标识 |
| `subjectId`          | `string`               | `false`         | `false`            | `subject_id`              | `varchar(36)`      |                       |                        | 关联的科目ID |
| `name`               | `string`               | `false`         | `false`            | `name`                    | `varchar(100)`     |                       |                        | 套餐名称 |
| `price`              | `number`               | `false`         | `false`            | `price`                   | `decimal(10,2)`    |                       |                        | 价格 |
| `validDays`          | `number`               | `false`         | `false`            | `valid_days`              | `int`              |                       |                        | 有效天数 |
| `isDefault`          | `boolean`              | `false`         | `false`            | `is_default`              | `boolean`          | `false`               |                        | 是否默认套餐 |
| `status`             | `number`               | `false`         | `false`            | `status`                  | `smallint`         | `0`                   | `dict: ["正常", "禁用"]` | 状态 (0-正常, 1-禁用) |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间 |

**移除的 Prisma 关系字段**:
* `subject: Subject` (通过 `subjectId` 关联)
* `orders: Order[]`

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 题目信息 (Question Entity)

**实体说明**: 存储题库中的题目信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 题目唯一标识 |
| `subjectId`          | `string`               | `false`         | `false`            | `subject_id`              | `varchar(36)`      |                       |                        | 所属科目ID |
| `type`               | `number`               | `false`         | `false`            | `type`                    | `smallint`         |                       | `dict: ["单选", "多选", "判断"]` | 题目类型 (0-单选, 1-多选, 2-判断) |
| `content`            | `string`               | `false`         | `false`            | `content`                 | `text`             |                       |                        | 题目内容 |
| `analysis`           | `string`               | `true`          | `false`            | `analysis`                | `text`             |                       |                        | 题目解析 |
| `difficulty`         | `number`               | `false`         | `false`            | `difficulty`              | `smallint`         | `0`                   | `dict: ["简单", "中等", "困难"]` | 难度级别 (0-简单, 1-中等, 2-困难) |
| `orderNum`           | `number`               | `false`         | `false`            | `order_num`               | `int`              | `0`                   |                        | 排序号 |
| `status`             | `number`               | `false`         | `false`            | `status`                  | `smallint`         | `0`                   | `dict: ["正常", "禁用"]` | 状态 (0-正常, 1-禁用) |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间 |
| `updatedAt`          | `Date`                 | `false`         | `false`            | `updated_at`              | `datetime`         |                       |                        | 更新时间 |

**注意**: 
* `type` 字段：Prisma 原定义为 `Int @db.SmallInt // 1-单选 2-多选 3-判断`。根据 cool-admin v8.x 的约定，这里调整为 `dict: ["单选", "多选", "判断"]`，数据库中存储 `0` 代表 "单选"，`1` 代表 "多选"，`2` 代表 "判断"。
* `difficulty` 字段：Prisma 原定义为 `Int @default(1) @db.SmallInt // 1-简单 2-中等 3-困难`。这里调整为 `dict: ["简单", "中等", "困难"]`，数据库中存储 `0` 代表 "简单"，`1` 代表 "中等"，`2` 代表 "困难"，默认值为 `0` ("简单")。

**移除的 Prisma 关系字段**:
* `subject: Subject` (通过 `subjectId` 关联)
* `options: QuestionOption[]`
* `studyRecords: StudyRecord[]`
* `wrongQuestions: WrongQuestion[]`
* `favoriteQuestions: FavoriteQuestion[]`

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 题目选项 (QuestionOption Entity)

**实体说明**: 存储题目的选项信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 选项唯一标识 |
| `questionId`         | `string`               | `false`         | `false`            | `question_id`             | `varchar(36)`      |                       |                        | 关联的题目ID |
| `content`            | `string`               | `false`         | `false`            | `content`                 | `text`             |                       |                        | 选项内容 |
| `isCorrect`          | `boolean`              | `false`         | `false`            | `is_correct`              | `boolean`          | `false`               |                        | 是否为正确选项 |
| `orderNum`           | `number`               | `false`         | `false`            | `order_num`               | `int`              | `0`                   |                        | 排序号 |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间 |

**移除的 Prisma 关系字段**:
* `question: Question` (通过 `questionId` 关联)

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

## 交易体系模型

### 订单信息 (Order Entity)

**实体说明**: 存储用户购买科目的订单信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 订单唯一标识 |
| `orderNo`            | `string`               | `false`         | `false`            | `order_no`                | `varchar(32)`      |                       |                        | 订单编号 (唯一) |
| `userId`             | `string`               | `false`         | `false`            | `user_id`                 | `varchar(36)`      |                       |                        | 用户ID |
| `subjectId`          | `string`               | `false`         | `false`            | `subject_id`              | `varchar(36)`      |                       |                        | 科目ID |
| `priceId`            | `string`               | `false`         | `false`            | `price_id`                | `varchar(36)`      |                       |                        | 价格方案ID |
| `amount`             | `number`               | `false`         | `false`            | `amount`                  | `decimal(10,2)`    |                       |                        | 订单金额 |
| `payAmount`          | `number`               | `false`         | `false`            | `pay_amount`              | `decimal(10,2)`    | `0`                   |                        | 实际支付金额 |
| `status`             | `number`               | `false`         | `false`            | `status`                  | `smallint`         | `0`                   | `dict: ["待支付", "已支付", "已取消", "异常"]` | 订单状态 (0-待支付, 1-已支付, 2-已取消, 3-异常) |
| `validUntil`         | `Date`                 | `true`          | `false`            | `valid_until`             | `datetime`         |                       |                        | 权限有效期限 |
| `distributorId`      | `string`               | `true`          | `false`            | `distributor_id`          | `varchar(36)`      |                       |                        | 推广人ID |
| `paidAt`             | `Date`                 | `true`          | `false`            | `paid_at`                 | `datetime`         |                       |                        | 支付时间 |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间 |
| `updatedAt`          | `Date`                 | `false`         | `false`            | `updated_at`              | `datetime`         |                       |                        | 更新时间 |

**注意**: 
* `status` 字段：Prisma 原定义为 `Int @default(0) @db.SmallInt // 0-待支付 1-已支付 2-已取消 3-异常`。根据 cool-admin v8.x 的约定，这里调整为 `dict: ["待支付", "已支付", "已取消", "异常"]`，数据库中存储值与数组索引对应。

**移除的 Prisma 关系字段**:
* `user: User` (通过 `userId` 关联)
* `subject: Subject` (通过 `subjectId` 关联)
* `price: SubjectPrice` (通过 `priceId` 关联)
* `payments: PaymentRecord[]`

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 支付记录 (PaymentRecord Entity)

**实体说明**: 存储订单的支付记录信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 支付记录唯一标识 |
| `orderId`            | `string`               | `false`         | `false`            | `order_id`                | `varchar(36)`      |                       |                        | 关联的订单ID |
| `paymentType`        | `number`               | `false`         | `false`            | `payment_type`            | `smallint`         |                       | `dict: ["微信支付", "支付宝支付", "激活码"]` | 支付类型 (0-微信支付, 1-支付宝支付, 2-激活码) |
| `amount`             | `number`               | `false`         | `false`            | `amount`                  | `decimal(10,2)`    |                       |                        | 支付金额 |
| `tradeNo`            | `string`               | `true`          | `false`            | `trade_no`                | `varchar(100)`     |                       |                        | 内部交易号 |
| `thirdTradeNo`       | `string`               | `true`          | `false`            | `third_trade_no`          | `varchar(100)`     |                       |                        | 第三方交易号 |
| `activationCode`     | `string`               | `true`          | `false`            | `activation_code`         | `varchar(50)`      |                       |                        | 激活码 |
| `status`             | `number`               | `false`         | `false`            | `status`                  | `smallint`         | `0`                   | `dict: ["待支付", "支付成功", "支付失败"]` | 支付状态 (0-待支付, 1-支付成功, 2-支付失败) |
| `paidAt`             | `Date`                 | `true`          | `false`            | `paid_at`                 | `datetime`         |                       |                        | 支付时间 |
| `failReason`         | `string`               | `true`          | `false`            | `fail_reason`             | `text`             |                       |                        | 失败原因 |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间 |

**移除的 Prisma 关系字段**:
* `order: Order` (通过 `orderId` 关联)

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 激活码管理 (ActivationCode Entity)

**实体说明**: 存储科目购买激活码信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 激活码唯一标识 |
| `code`               | `string`               | `false`         | `false`            | `code`                    | `varchar(20)`      |                       |                        | 激活码 (唯一) |
| `subjectId`          | `string`               | `false`         | `false`            | `subject_id`              | `varchar(36)`      |                       |                        | 关联的科目ID |
| `validDays`          | `number`               | `false`         | `false`            | `valid_days`              | `int`              |                       |                        | 有效天数 |
| `usedBy`             | `string`               | `true`          | `false`            | `used_by`                 | `varchar(36)`      |                       |                        | 使用者用户ID |
| `usedAt`             | `Date`                 | `true`          | `false`            | `used_at`                 | `datetime`         |                       |                        | 使用时间 |
| `status`             | `number`               | `false`         | `false`            | `status`                  | `smallint`         | `0`                   | `dict: ["未使用", "已使用", "已过期", "已禁用"]` | 激活码状态 (0-未使用, 1-已使用, 2-已过期, 3-已禁用) |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间 |
| `expiredAt`          | `Date`                 | `true`          | `false`            | `expired_at`              | `datetime`         |                       |                        | 过期时间 |

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

## 学习体系模型

### 学习记录 (StudyRecord Entity)

**实体说明**: 存储用户的学习记录信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 学习记录唯一标识 |
| `userId`             | `string`               | `false`         | `false`            | `user_id`                 | `varchar(36)`      |                       |                        | 用户ID |
| `subjectId`          | `string`               | `false`         | `false`            | `subject_id`              | `varchar(36)`      |                       |                        | 科目ID |
| `questionId`         | `string`               | `false`         | `false`            | `question_id`             | `varchar(36)`      |                       |                        | 题目ID |
| `practiceType`       | `number`               | `false`         | `false`            | `practice_type`           | `smallint`         |                       | `dict: ["顺序", "题型", "错题", "随机", "收藏", "搜题"]` | 练习类型 (0-顺序, 1-题型, 2-错题, 3-随机, 4-收藏, 5-搜题) |
| `userAnswer`         | `string`               | `true`          | `false`            | `user_answer`             | `text`             |                       |                        | 用户答案 |
| `isCorrect`          | `boolean`              | `true`          | `false`            | `is_correct`              | `boolean`          |                       |                        | 是否正确 |
| `timeSpent`          | `number`               | `false`         | `false`            | `time_spent`              | `int`              | `0`                   |                        | 答题耗时(秒) |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间 |

**注意**: 
* `practiceType` 字段：Prisma 原定义为 `Int @db.SmallInt // 1-顺序 2-题型 3-错题 4-随机 5-收藏 6-搜题`。根据 cool-admin v8.x 的约定，这里调整为 `dict: ["顺序", "题型", "错题", "随机", "收藏", "搜题"]`，数据库中存储 `0` 代表 "顺序"，`1` 代表 "题型"，依此类推。
* 原有 `@@unique([userId, questionId])` 约束表示用户和题目的组合是唯一的。

**移除的 Prisma 关系字段**:
* `user: User` (通过 `userId` 关联)
* `subject: Subject` (通过 `subjectId` 关联)
* `question: Question` (通过 `questionId` 关联)

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 考试记录 (ExamRecord Entity)

**实体说明**: 存储用户的考试记录信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 考试记录唯一标识 |
| `userId`             | `string`               | `false`         | `false`            | `user_id`                 | `varchar(36)`      |                       |                        | 用户ID |
| `subjectId`          | `string`               | `false`         | `false`            | `subject_id`              | `varchar(36)`      |                       |                        | 科目ID |
| `score`              | `number`               | `false`         | `false`            | `score`                   | `int`              | `0`                   |                        | 考试得分 |
| `totalScore`         | `number`               | `false`         | `false`            | `total_score`             | `int`              |                       |                        | 总分 |
| `passScore`          | `number`               | `false`         | `false`            | `pass_score`              | `int`              |                       |                        | 及格分 |
| `isPassed`           | `boolean`              | `false`         | `false`            | `is_passed`               | `boolean`          | `false`               |                        | 是否及格 |
| `timeSpent`          | `number`               | `false`         | `false`            | `time_spent`              | `int`              |                       |                        | 考试耗时(秒) |
| `answers`            | `object`               | `false`         | `false`            | `answers`                 | `json`             |                       |                        | 答题详情 |
| `startedAt`          | `Date`                 | `false`         | `false`            | `started_at`              | `datetime`         |                       |                        | 开始时间 |
| `finishedAt`         | `Date`                 | `true`          | `false`            | `finished_at`             | `datetime`         |                       |                        | 完成时间 |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间 |

**移除的 Prisma 关系字段**:
* `user: User` (通过 `userId` 关联)
* `subject: Subject` (通过 `subjectId` 关联)

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 错题记录 (WrongQuestion Entity)

**实体说明**: 存储用户的错题记录信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 错题记录唯一标识 |
| `userId`             | `string`               | `false`         | `false`            | `user_id`                 | `varchar(36)`      |                       |                        | 用户ID |
| `questionId`         | `string`               | `false`         | `false`            | `question_id`             | `varchar(36)`      |                       |                        | 题目ID |
| `wrongCount`         | `number`               | `false`         | `false`            | `wrong_count`             | `int`              | `1`                   |                        | 错误次数 |
| `lastWrongAt`        | `Date`                 | `false`         | `false`            | `last_wrong_at`           | `datetime`         |                       |                        | 最后错误时间 |
| `isMastered`         | `boolean`              | `false`         | `false`            | `is_mastered`             | `boolean`          | `false`               |                        | 是否已掌握 |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间 |
| `updatedAt`          | `Date`                 | `false`         | `false`            | `updated_at`              | `datetime`         |                       |                        | 更新时间 |

**注意**:
* 原有 `@@unique([userId, questionId])` 约束表示用户和题目的组合是唯一的。

**移除的 Prisma 关系字段**:
* `user: User` (通过 `userId` 关联)
* `question: Question` (通过 `questionId` 关联)

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 收藏题目 (FavoriteQuestion Entity)

**实体说明**: 存储用户收藏的题目信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 收藏记录唯一标识 |
| `userId`             | `string`               | `false`         | `false`            | `user_id`                 | `varchar(36)`      |                       |                        | 用户ID |
| `questionId`         | `string`               | `false`         | `false`            | `question_id`             | `varchar(36)`      |                       |                        | 题目ID |
| `note`               | `string`               | `true`          | `false`            | `note`                    | `text`             |                       |                        | 个人备注 |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间 |

**注意**:
* 原有 `@@unique([userId, questionId])` 约束表示用户和题目的组合是唯一的。

**移除的 Prisma 关系字段**:
* `user: User` (通过 `userId` 关联)
* `question: Question` (通过 `questionId` 关联)

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

## 微信体系模型

### 微信消息记录 (WechatMessage Entity)

**实体说明**: 存储微信公众号/小程序的消息记录。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 消息记录唯一标识 |
| `fromUser`           | `string`               | `false`         | `false`            | `from_user`               | `varchar(100)`     |                       |                        | 发送方OpenID |
| `toUser`             | `string`               | `false`         | `false`            | `to_user`                 | `varchar(100)`     |                       |                        | 接收方OpenID |
| `msgType`            | `string`               | `false`         | `false`            | `msg_type`                | `varchar(20)`      |                       |                        | 消息类型 |
| `content`            | `string`               | `true`          | `false`            | `content`                 | `text`             |                       |                        | 消息内容 |
| `mediaId`            | `string`               | `true`          | `false`            | `media_id`                | `varchar(100)`     |                       |                        | 媒体文件ID |
| `event`              | `string`               | `true`          | `false`            | `event`                   | `varchar(50)`      |                       |                        | 事件类型 |
| `eventKey`           | `string`               | `true`          | `false`            | `event_key`               | `varchar(100)`     |                       |                        | 事件KEY |
| `msgId`              | `string`               | `true`          | `false`            | `msg_id`                  | `varchar(100)`     |                       |                        | 消息ID |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间 |

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 微信菜单配置 (WechatMenu Entity)

**实体说明**: 存储微信公众号菜单配置信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 菜单配置唯一标识 |
| `name`               | `string`               | `false`         | `false`            | `name`                    | `varchar(50)`      |                       |                        | 菜单名称 |
| `type`               | `string`               | `false`         | `false`            | `type`                    | `varchar(20)`      |                       |                        | 菜单类型 |
| `key`                | `string`               | `true`          | `false`            | `key`                     | `varchar(100)`     |                       |                        | 菜单KEY |
| `url`                | `string`               | `true`          | `false`            | `url`                     | `varchar(500)`     |                       |                        | 跳转URL |
| `appId`              | `string`               | `true`          | `false`            | `app_id`                  | `varchar(100)`     |                       |                        | 小程序AppID |
| `pagePath`           | `string`               | `true`          | `false`            | `page_path`               | `varchar(200)`     |                       |                        | 小程序页面路径 |
| `parentId`           | `string`               | `true`          | `false`            | `parent_id`               | `varchar(36)`      |                       |                        | 父菜单ID |
| `orderNum`           | `number`               | `false`         | `false`            | `order_num`               | `int`              | `0`                   |                        | 排序号 |
| `status`             | `number`               | `false`         | `false`            | `status`                  | `smallint`         | `0`                   | `dict: ["正常", "禁用"]` | 状态 (0-正常, 1-禁用) |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间 |
| `updatedAt`          | `Date`                 | `false`         | `false`            | `updated_at`              | `datetime`         |                       |                        | 更新时间 |

**注意**:
* `status` 字段：Prisma 原定义为 `Int @default(1) @db.SmallInt`。根据 cool-admin v8.x 的约定，这里调整为 `dict: ["正常", "禁用"]`，数据库中存储 `0` 代表 "正常"，`1` 代表 "禁用"，默认值为 `0` ("正常")。

**移除的 Prisma 关系字段**:
* `parent: WechatMenu?` (通过 `parentId` 关联)
* `children: WechatMenu[]`

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

## 管理体系模型

### 管理员用户 (AdminUser Entity)

**实体说明**: 存储系统管理员信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 管理员唯一标识 |
| `username`           | `string`               | `false`         | `false`            | `username`                | `varchar(50)`      |                       |                        | 用户名 (唯一) |
| `password`           | `string`               | `false`         | `false`            | `password`                | `varchar(255)`     |                       |                        | 加密密码 |
| `realName`           | `string`               | `false`         | `false`            | `real_name`               | `varchar(50)`      |                       |                        | 真实姓名 |
| `email`              | `string`               | `true`          | `false`            | `email`                   | `varchar(100)`     |                       |                        | 邮箱 (唯一) |
| `phone`              | `string`               | `true`          | `false`            | `phone`                   | `varchar(20)`      |                       |                        | 手机号 |
| `avatar`             | `string`               | `true`          | `false`            | `avatar`                  | `varchar(500)`     |                       |                        | 头像 |
| `role`               | `string`               | `false`         | `false`            | `role`                    | `varchar(20)`      | `'admin'`             |                        | 角色 |
| `status`             | `number`               | `false`         | `false`            | `status`                  | `smallint`         | `0`                   | `dict: ["正常", "禁用"]` | 状态 (0-正常, 1-禁用) |
| `lastLogin`          | `Date`                 | `true`          | `false`            | `last_login`              | `datetime`         |                       |                        | 最后登录时间 |
| `loginCount`         | `number`               | `false`         | `false`            | `login_count`             | `int`              | `0`                   |                        | 登录次数 |
| `createdBy`          | `string`               | `true`          | `false`            | `created_by`              | `varchar(36)`      |                       |                        | 创建人 |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间 |
| `updatedAt`          | `Date`                 | `false`         | `false`            | `updated_at`              | `datetime`         |                       |                        | 更新时间 |

**注意**:
* `status` 字段：Prisma 原定义为 `Int @default(1) @db.SmallInt // 状态：1-正常 2-禁用`。根据 cool-admin v8.x 的约定，这里调整为 `dict: ["正常", "禁用"]`，数据库中存储 `0` 代表 "正常"，`1` 代表 "禁用"，默认值为 `0` ("正常")。

**移除的 Prisma 关系字段**:
* `createdLogs: OperationLog[]`

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 系统配置 (SystemConfig Entity)

**实体说明**: 存储系统配置信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 配置唯一标识 |
| `key`                | `string`               | `false`         | `false`            | `key`                     | `varchar(100)`     |                       |                        | 配置键 (唯一) |
| `value`              | `string`               | `false`         | `false`            | `value`                   | `text`             |                       |                        | 配置值 |
| `type`               | `string`               | `false`         | `false`            | `type`                    | `varchar(20)`      | `'string'`            |                        | 数据类型 |
| `group`              | `string`               | `false`         | `false`            | `group`                   | `varchar(50)`      | `'system'`            |                        | 配置分组 |
| `name`               | `string`               | `false`         | `false`            | `name`                    | `varchar(100)`     |                       |                        | 配置名称 |
| `description`        | `string`               | `true`          | `false`            | `description`             | `varchar(500)`     |                       |                        | 配置描述 |
| `isPublic`           | `boolean`              | `false`         | `false`            | `is_public`               | `boolean`          | `false`               |                        | 是否公开 |
| `isRequired`         | `boolean`              | `false`         | `false`            | `is_required`             | `boolean`          | `false`               |                        | 是否必需 |
| `sortOrder`          | `number`               | `false`         | `false`            | `sort_order`              | `int`              | `0`                   |                        | 排序 |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间 |
| `updatedAt`          | `Date`                 | `false`         | `false`            | `updated_at`              | `datetime`         |                       |                        | 更新时间 |

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 操作日志 (OperationLog Entity)

**实体说明**: 存储系统操作日志信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 操作日志唯一标识 |
| `adminId`            | `string`               | `true`          | `false`            | `admin_id`                | `varchar(36)`      |                       |                        | 操作管理员ID |
| `userType`           | `string`               | `false`         | `false`            | `user_type`               | `varchar(20)`      |                       |                        | 用户类型：admin, user |
| `action`             | `string`               | `false`         | `false`            | `action`                  | `varchar(100)`     |                       |                        | 操作类型 |
| `module`             | `string`               | `false`         | `false`            | `module`                  | `varchar(50)`      |                       |                        | 模块名称 |
| `target`             | `string`               | `true`          | `false`            | `target`                  | `varchar(100)`     |                       |                        | 操作目标 |
| `content`            | `string`               | `true`          | `false`            | `content`                 | `text`             |                       |                        | 操作内容 |
| `ip`                 | `string`               | `true`          | `false`            | `ip`                      | `varchar(50)`      |                       |                        | IP地址 |
| `userAgent`          | `string`               | `true`          | `false`            | `user_agent`              | `text`             |                       |                        | 用户代理 |
| `result`             | `string`               | `false`         | `false`            | `result`                  | `varchar(20)`      | `'success'`           |                        | 操作结果 |
| `errorMsg`           | `string`               | `true`          | `false`            | `error_msg`               | `text`             |                       |                        | 错误信息 |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间 |

**移除的 Prisma 关系字段**:
* `admin: AdminUser?` (通过 `adminId` 关联)

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 佣金提现记录 (WithdrawRecord Entity)

**实体说明**: 存储分销商佣金提现记录信息。

| 属性名 (Property Name) | 类型 (TypeScript Type) | 可空 (Nullable) | 主键 (Primary Key) | 数据库列名 (DB Column Name) | 数据库类型 (DB Type) | 默认值 (DB Default) | CoolAdmin Dict/Options | 注释 (Comment) |
|----------------------|------------------------|-----------------|--------------------|---------------------------|--------------------|-----------------------|------------------------|----------------|
| `id`                 | `string`               | `false`         | `true`             | `id`                      | `varchar(36)`      |                       |                        | 提现记录唯一标识 |
| `distributorId`      | `string`               | `false`         | `false`            | `distributor_id`          | `varchar(36)`      |                       |                        | 分销商ID |
| `amount`             | `number`               | `false`         | `false`            | `amount`                  | `decimal(10,2)`    |                       |                        | 提现金额 |
| `fee`                | `number`               | `false`         | `false`            | `fee`                     | `decimal(10,2)`    | `0`                   |                        | 手续费 |
| `actualAmount`       | `number`               | `false`         | `false`            | `actual_amount`           | `decimal(10,2)`    |                       |                        | 实际到账金额 |
| `withdrawType`       | `number`               | `false`         | `false`            | `withdraw_type`           | `smallint`         |                       | `dict: ["支付宝", "微信", "银行卡"]` | 提现类型 (0-支付宝, 1-微信, 2-银行卡) |
| `account`            | `string`               | `false`         | `false`            | `account`                 | `varchar(100)`     |                       |                        | 提现账号 |
| `accountName`        | `string`               | `false`         | `false`            | `account_name`            | `varchar(50)`      |                       |                        | 账户名 |
| `status`             | `number`               | `false`         | `false`            | `status`                  | `smallint`         | `0`                   | `dict: ["待审核", "已通过", "已拒绝", "已到账"]` | 状态 (0-待审核, 1-已通过, 2-已拒绝, 3-已到账) |
| `remark`             | `string`               | `true`          | `false`            | `remark`                  | `text`             |                       |                        | 备注 |
| `reviewedBy`         | `string`               | `true`          | `false`            | `reviewed_by`             | `varchar(36)`      |                       |                        | 审核人 |
| `reviewedAt`         | `Date`                 | `true`          | `false`            | `reviewed_at`             | `datetime`         |                       |                        | 审核时间 |
| `createdAt`          | `Date`                 | `false`         | `false`            | `created_at`              | `datetime`         |                       |                        | 创建时间 |
| `updatedAt`          | `Date`                 | `false`         | `false`            | `updated_at`              | `datetime`         |                       |                        | 更新时间 |

**移除的 Prisma 关系字段**:
* `distributor: Distributor` (通过 `distributorId` 关联)

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)