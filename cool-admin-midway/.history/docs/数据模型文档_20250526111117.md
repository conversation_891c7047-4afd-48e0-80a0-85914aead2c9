# 考理论教育平台 - 数据模型文档

## 核心实体关系

基于 Cool-Admin 8.x 和 TypeORM 0.3.20 设计的无外键关系数据模型 (通过 ID 字段关联):

```
用户(User)
    ↑ userId
    ├─────── 订单(Order) ─── subjectId ──→ 科目(Subject)
    ├─────── 学习记录(StudyRecord) ─── questionId ──→ 题目(Question)
    ├─────── 考试记录(ExamRecord) ─── examId ──→ 考试(Exam)
    ├─────── 错题记录(WrongQuestion) ─── questionId ──→ 题目(Question)
    ├─────── 收藏题目(FavoriteQuestion) ─── questionId ──→ 题目(Question)
    └─────── 分销商(Distributor)

科目(Subject)
    ↑ subjectId
    ├─────── 题目(Question) ─── questionId ──→ 选项(QuestionOption)
    ├─────── 考试(Exam)
    └─────── 价格方案(SubjectPrice)
```

## TypeORM 实体说明

所有实体均继承自 `BaseEntity`，位于 `src/modules/base/entity/base.ts`。BaseEntity 已经包含以下字段：
- id: 主键，自增
- createTime: 创建时间
- updateTime: 更新时间

数据库设计遵循 Cool-Admin 8.x 规范：
- 不使用外键关系，如 @ManyToOne、@OneToMany 等
- 使用 dict 属性定义枚举值
- 实体字段使用驼峰命名
- TypeORM 版本 0.3.20

## 用户体系模型

### 用户基础信息 (User Entity)

**实体说明**: 存储平台用户的基本信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `nickname` | `string` | `true` | `普通索引` | `nickname` | `varchar(50)` | | | 昵称 |
| `avatar` | `string` | `true` | | `avatar` | `varchar(500)` | | | 头像URL |
| `phone` | `string` | `true` | `唯一索引` | `phone` | `varchar(20)` | | | 手机号码 |
| `city` | `string` | `true` | | `city` | `varchar(50)` | | | 城市 |
| `province` | `string` | `true` | | `province` | `varchar(50)` | | | 省份 |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['正常', '禁用']` | 状态 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |
| `updateTime` | `Date` | `false` | | `updateTime` | `datetime` | 当前时间 | | 更新时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 用户基础信息
 */
@Entity('user')
export class UserEntity extends BaseEntity {
  @Index()
  @Column({ comment: '昵称', nullable: true, length: 50 })
  nickname: string;

  @Column({ comment: '头像URL', nullable: true, length: 500 })
  avatar: string;

  @Index({ unique: true })
  @Column({ comment: '手机号码', nullable: true, length: 20 })
  phone: string;

  @Column({ comment: '城市', nullable: true, length: 50 })
  city: string;

  @Column({ comment: '省份', nullable: true, length: 50 })
  province: string;

  @Column({ 
    comment: '状态', 
    default: 0,
    type: 'tinyint',
    dict: ['正常', '禁用']
  })
  status: number;
}
```



**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 微信账号信息 (WechatAccount Entity)

**实体说明**: 存储用户绑定的微信账号信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `userId` | `number` | `false` | `普通索引` | `userId` | `int` | | | 关联的用户ID |
| `unionId` | `string` | `true` | `唯一索引` | `unionId` | `varchar(100)` | | | 微信 UnionID |
| `openId` | `string` | `false` | `唯一索引` | `openId` | `varchar(100)` | | | 微信 OpenID |
| `platform` | `number` | `false` | | `platform` | `tinyint` | | `dict: ['小程序', '公众号']` | 平台 |
| `nickname` | `string` | `true` | | `nickname` | `varchar(50)` | | | 微信昵称 |
| `avatar` | `string` | `true` | | `avatar` | `varchar(500)` | | | 微信头像URL |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 微信账号信息
 */
@Entity('wechat_account')
export class WechatAccountEntity extends BaseEntity {
  @Index()
  @Column({ comment: '关联的用户ID', type: 'int' })
  userId: number;

  @Index({ unique: true })
  @Column({ comment: '微信 UnionID', nullable: true, length: 100 })
  unionId: string;

  @Index({ unique: true })
  @Column({ comment: '微信 OpenID', length: 100 })
  openId: string;

  @Column({ 
    comment: '平台', 
    type: 'tinyint',
    dict: ['小程序', '公众号']
  })
  platform: number;

  @Column({ comment: '微信昵称', nullable: true, length: 50 })
  nickname: string;

  @Column({ comment: '微信头像URL', nullable: true, length: 500 })
  avatar: string;
}
```

**移除的 Prisma 关系字段**:
* `wechatAccounts: WechatAccount[]`
* `orders: Order[]`
* `studyRecords: StudyRecord[]`
* `examRecords: ExamRecord[]`
* `wrongQuestions: WrongQuestion[]`
* `favoriteQuestions: FavoriteQuestion[]`
* `distributor: Distributor?`

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 分销商信息 (Distributor Entity)

**实体说明**: 存储分销商的详细信息，包括层级、佣金、收益等。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `userId` | `number` | `false` | `唯一索引` | `userId` | `int` | | | 关联的用户ID |
| `parentId` | `number` | `true` | `普通索引` | `parentId` | `int` | | | 上级分销商ID |
| `level` | `number` | `false` | | `level` | `tinyint` | `1` | | 分销层级 |
| `commission` | `number` | `false` | | `commission` | `decimal(5,4)` | `0.15` | | 佣金比例 |
| `totalEarned` | `number` | `false` | | `totalEarned` | `decimal(10,2)` | `0` | | 累计收益 |
| `balance` | `number` | `false` | | `balance` | `decimal(10,2)` | `0` | | 可提现余额 |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['正常', '禁用']` | 状态 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |
| `updateTime` | `Date` | `false` | | `updateTime` | `datetime` | 当前时间 | | 更新时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 分销商信息
 */
@Entity('distributor')
export class DistributorEntity extends BaseEntity {
  @Index({ unique: true })
  @Column({ comment: '关联的用户ID', type: 'int' })
  userId: number;

  @Index()
  @Column({ comment: '上级分销商ID', type: 'int', nullable: true })
  parentId: number;

  @Column({ comment: '分销层级', type: 'tinyint', default: 1 })
  level: number;

  @Column({ comment: '佣金比例', type: 'decimal', precision: 5, scale: 4, default: 0.15 })
  commission: number;

  @Column({ comment: '累计收益', type: 'decimal', precision: 10, scale: 2, default: 0 })
  totalEarned: number;

  @Column({ comment: '可提现余额', type: 'decimal', precision: 10, scale: 2, default: 0 })
  balance: number;

  @Column({ 
    comment: '状态', 
    type: 'tinyint',
    default: 0,
    dict: ['正常', '禁用']
  })
  status: number;
}
```

## 内容体系模型

### 分类信息 (Category Entity)

**实体说明**: 存储内容分类的层级结构信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `name` | `string` | `false` | | `name` | `varchar(100)` | | | 分类名称 |
| `parentId` | `number` | `true` | `普通索引` | `parentId` | `int` | | | 父分类ID |
| `level` | `number` | `false` | | `level` | `tinyint` | `1` | | 层级 |
| `orderNum` | `number` | `false` | | `orderNum` | `int` | `0` | | 排序号 |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['正常', '禁用']` | 状态 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 分类信息
 */
@Entity('category')
export class CategoryEntity extends BaseEntity {
  @Column({ comment: '分类名称', length: 100 })
  name: string;

  @Index()
  @Column({ comment: '父分类ID', type: 'int', nullable: true })
  parentId: number;

  @Column({ comment: '层级', type: 'tinyint', default: 1 })
  level: number;

  @Column({ comment: '排序号', type: 'int', default: 0 })
  orderNum: number;

  @Column({ 
    comment: '状态', 
    type: 'tinyint',
    default: 0,
    dict: ['正常', '禁用']
  })
  status: number;
}
```

### 科目信息 (Subject Entity)

**实体说明**: 存储教育平台的科目信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `name` | `string` | `false` | | `name` | `varchar(200)` | | | 科目名称 |
| `description` | `string` | `true` | | `description` | `text` | | | 科目描述 |
| `categoryId` | `number` | `false` | `普通索引` | `categoryId` | `int` | | | 分类ID |
| `displayTemplate` | `string` | `true` | | `displayTemplate` | `varchar(200)` | | | 显示模板 |
| `questionCount` | `number` | `false` | | `questionCount` | `int` | `0` | | 题目数量 |
| `orderNum` | `number` | `false` | | `orderNum` | `int` | `0` | | 排序号 |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['上架', '下架']` | 状态 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |
| `updateTime` | `Date` | `false` | | `updateTime` | `datetime` | 当前时间 | | 更新时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 科目信息
 */
@Entity('subject')
export class SubjectEntity extends BaseEntity {
  @Column({ comment: '科目名称', length: 200 })
  name: string;

  @Column({ comment: '科目描述', type: 'text', nullable: true })
  description: string;
  
  @Index()
  @Column({ comment: '分类ID', type: 'int' })
  categoryId: number;
  
  @Column({ comment: '显示模板', nullable: true, length: 200 })
  displayTemplate: string;
  
  @Column({ comment: '题目数量', type: 'int', default: 0 })
  questionCount: number;
  
  @Column({ comment: '排序号', type: 'int', default: 0 })
  orderNum: number;

  @Column({ 
    comment: '状态', 
    type: 'tinyint',
    default: 0,
    dict: ['上架', '下架']
  })
  status: number;
}
```

### 科目价格方案 (SubjectPrice Entity)

**实体说明**: 存储科目的不同价格套餐方案。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `subjectId` | `number` | `false` | `普通索引` | `subjectId` | `int` | | | 关联的科目ID |
| `name` | `string` | `false` | | `name` | `varchar(100)` | | | 套餐名称 |
| `price` | `number` | `false` | | `price` | `decimal(10,2)` | | | 价格 |
| `validDays` | `number` | `false` | | `validDays` | `int` | | | 有效天数 |
| `isDefault` | `boolean` | `false` | | `isDefault` | `boolean` | `false` | | 是否默认套餐 |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['正常', '禁用']` | 状态 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 科目价格方案
 */
@Entity('subject_price')
export class SubjectPriceEntity extends BaseEntity {
  @Index()
  @Column({ comment: '关联的科目ID', type: 'int' })
  subjectId: number;

  @Column({ comment: '套餐名称', length: 100 })
  name: string;

  @Column({ comment: '价格', type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @Column({ comment: '有效天数', type: 'int' })
  validDays: number;

  @Column({ comment: '是否默认套餐', type: 'boolean', default: false })
  isDefault: boolean;

  @Column({ 
    comment: '状态', 
    type: 'tinyint',
    default: 0,
    dict: ['正常', '禁用']
  })
  status: number;
}
```

### 题目信息 (Question Entity)

**实体说明**: 存储题库中的题目信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `subjectId` | `number` | `false` | `普通索引` | `subjectId` | `int` | | | 所属科目ID |
| `type` | `number` | `false` | | `type` | `tinyint` | | `dict: ['单选', '多选', '判断']` | 题目类型 |
| `content` | `string` | `false` | | `content` | `text` | | | 题目内容 |
| `analysis` | `string` | `true` | | `analysis` | `text` | | | 题目解析 |
| `difficulty` | `number` | `false` | | `difficulty` | `tinyint` | `0` | `dict: ['简单', '中等', '困难']` | 难度级别 |
| `orderNum` | `number` | `false` | | `orderNum` | `int` | `0` | | 排序号 |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['正常', '禁用']` | 状态 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |
| `updateTime` | `Date` | `false` | | `updateTime` | `datetime` | 当前时间 | | 更新时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 题目信息
 */
@Entity('question')
export class QuestionEntity extends BaseEntity {
  @Index()
  @Column({ comment: '所属科目ID', type: 'int' })
  subjectId: number;

  @Column({
    comment: '题目类型',
    type: 'tinyint',
    dict: ['单选', '多选', '判断']
  })
  type: number;

  @Column({ comment: '题目内容', type: 'text' })
  content: string;

  @Column({ comment: '题目解析', type: 'text', nullable: true })
  analysis: string;

  @Column({
    comment: '难度级别',
    type: 'tinyint',
    default: 0,
    dict: ['简单', '中等', '困难']
  })
  difficulty: number;
  
  @Column({ comment: '排序号', type: 'int', default: 0 })
  orderNum: number;

  @Column({ 
    comment: '状态', 
    type: 'tinyint',
    default: 0,
    dict: ['正常', '禁用']
  })
  status: number;
}
```

### 题目选项 (QuestionOption Entity)

**实体说明**: 存储题目的选项信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `questionId` | `number` | `false` | `普通索引` | `questionId` | `int` | | | 关联的题目ID |
| `content` | `string` | `false` | | `content` | `text` | | | 选项内容 |
| `isCorrect` | `boolean` | `false` | | `isCorrect` | `boolean` | `false` | | 是否为正确选项 |
| `orderNum` | `number` | `false` | | `orderNum` | `int` | `0` | | 排序号 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 题目选项
 */
@Entity('question_option')
export class QuestionOptionEntity extends BaseEntity {
  @Index()
  @Column({ comment: '关联的题目ID', type: 'int' })
  questionId: number;

  @Column({ comment: '选项内容', type: 'text' })
  content: string;

  @Column({ comment: '是否为正确选项', type: 'boolean', default: false })
  isCorrect: boolean;

  @Column({ comment: '排序号', type: 'int', default: 0 })
  orderNum: number;
}
```

## 交易体系模型

### 订单信息 (Order Entity)

**实体说明**: 存储用户购买科目的订单信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `orderNo` | `string` | `false` | `唯一索引` | `orderNo` | `varchar(32)` | | | 订单编号 |
| `userId` | `number` | `false` | `普通索引` | `userId` | `int` | | | 用户ID |
| `subjectId` | `number` | `false` | `普通索引` | `subjectId` | `int` | | | 科目ID |
| `priceId` | `number` | `false` | `普通索引` | `priceId` | `int` | | | 价格方案ID |
| `amount` | `number` | `false` | | `amount` | `decimal(10,2)` | | | 订单金额 |
| `payAmount` | `number` | `false` | | `payAmount` | `decimal(10,2)` | `0` | | 实际支付金额 |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['待支付', '已支付', '已取消', '异常']` | 订单状态 |
| `validUntil` | `Date` | `true` | | `validUntil` | `datetime` | | | 权限有效期限 |
| `distributorId` | `number` | `true` | `普通索引` | `distributorId` | `int` | | | 推广人ID |
| `paidAt` | `Date` | `true` | | `paidAt` | `datetime` | | | 支付时间 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |
| `updateTime` | `Date` | `false` | | `updateTime` | `datetime` | 当前时间 | | 更新时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 订单信息
 */
@Entity('order')
export class OrderEntity extends BaseEntity {
  @Index({ unique: true })
  @Column({ comment: '订单编号', length: 32 })
  orderNo: string;

  @Index()
  @Column({ comment: '用户ID', type: 'int' })
  userId: number;

  @Index()
  @Column({ comment: '科目ID', type: 'int' })
  subjectId: number;

  @Index()
  @Column({ comment: '价格方案ID', type: 'int' })
  priceId: number;

  @Column({ comment: '订单金额', type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ comment: '实际支付金额', type: 'decimal', precision: 10, scale: 2, default: 0 })
  payAmount: number;

  @Column({
    comment: '订单状态',
    type: 'tinyint',
    default: 0,
    dict: ['待支付', '已支付', '已取消', '异常']
  })
  status: number;

  @Column({ comment: '权限有效期限', type: 'datetime', nullable: true })
  validUntil: Date;

  @Index()
  @Column({ comment: '推广人ID', type: 'int', nullable: true })
  distributorId: number;

  @Column({ comment: '支付时间', type: 'datetime', nullable: true })
  paidAt: Date;
}
```

### 支付记录 (PaymentRecord Entity)

**实体说明**: 存储订单的支付记录信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `orderId` | `number` | `false` | `普通索引` | `orderId` | `int` | | | 关联的订单ID |
| `paymentType` | `number` | `false` | | `paymentType` | `tinyint` | | `dict: ['微信支付', '支付宝支付', '激活码']` | 支付类型 |
| `amount` | `number` | `false` | | `amount` | `decimal(10,2)` | | | 支付金额 |
| `tradeNo` | `string` | `true` | | `tradeNo` | `varchar(100)` | | | 内部交易号 |
| `thirdTradeNo` | `string` | `true` | | `thirdTradeNo` | `varchar(100)` | | | 第三方交易号 |
| `activationCode` | `string` | `true` | | `activationCode` | `varchar(50)` | | | 激活码 |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['待支付', '支付成功', '支付失败']` | 支付状态 |
| `paidAt` | `Date` | `true` | | `paidAt` | `datetime` | | | 支付时间 |
| `failReason` | `string` | `true` | | `failReason` | `text` | | | 失败原因 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 支付记录
 */
@Entity('payment_record')
export class PaymentRecordEntity extends BaseEntity {
  @Index()
  @Column({ comment: '关联的订单ID', type: 'int' })
  orderId: number;

  @Column({
    comment: '支付类型',
    type: 'tinyint',
    dict: ['微信支付', '支付宝支付', '激活码']
  })
  paymentType: number;

  @Column({ comment: '支付金额', type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ comment: '内部交易号', nullable: true, length: 100 })
  tradeNo: string;

  @Column({ comment: '第三方交易号', nullable: true, length: 100 })
  thirdTradeNo: string;

  @Column({ comment: '激活码', nullable: true, length: 50 })
  activationCode: string;

  @Column({
    comment: '支付状态',
    type: 'tinyint',
    default: 0,
    dict: ['待支付', '支付成功', '支付失败']
  })
  status: number;

  @Column({ comment: '支付时间', type: 'datetime', nullable: true })
  paidAt: Date;

  @Column({ comment: '失败原因', type: 'text', nullable: true })
  failReason: string;
}
```

### 激活码管理 (ActivationCode Entity)

**实体说明**: 存储科目购买激活码信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `code` | `string` | `false` | `唯一索引` | `code` | `varchar(20)` | | | 激活码 |
| `subjectId` | `number` | `false` | `普通索引` | `subjectId` | `int` | | | 关联的科目ID |
| `validDays` | `number` | `false` | | `validDays` | `int` | | | 有效天数 |
| `usedBy` | `number` | `true` | `普通索引` | `usedBy` | `int` | | | 使用者用户ID |
| `usedAt` | `Date` | `true` | | `usedAt` | `datetime` | | | 使用时间 |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['未使用', '已使用', '已过期', '已禁用']` | 激活码状态 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |
| `expiredAt` | `Date` | `true` | | `expiredAt` | `datetime` | | | 过期时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 激活码管理
 */
@Entity('activation_code')
export class ActivationCodeEntity extends BaseEntity {
  @Index({ unique: true })
  @Column({ comment: '激活码', length: 20 })
  code: string;

  @Index()
  @Column({ comment: '关联的科目ID', type: 'int' })
  subjectId: number;

  @Column({ comment: '有效天数', type: 'int' })
  validDays: number;

  @Index()
  @Column({ comment: '使用者用户ID', type: 'int', nullable: true })
  usedBy: number;

  @Column({ comment: '使用时间', type: 'datetime', nullable: true })
  usedAt: Date;

  @Column({
    comment: '激活码状态',
    type: 'tinyint',
    default: 0,
    dict: ['未使用', '已使用', '已过期', '已禁用']
  })
  status: number;

  @Column({ comment: '过期时间', type: 'datetime', nullable: true })
  expiredAt: Date;
}
```

## 学习体系模型

### 学习记录 (StudyRecord Entity)

**实体说明**: 存储用户的学习记录信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `userId` | `number` | `false` | `普通索引` | `userId` | `int` | | | 用户ID |
| `subjectId` | `number` | `false` | `普通索引` | `subjectId` | `int` | | | 科目ID |
| `questionId` | `number` | `false` | `普通索引` | `questionId` | `int` | | | 题目ID |
| `practiceType` | `number` | `false` | | `practiceType` | `tinyint` | | `dict: ['顺序', '题型', '错题', '随机', '收藏', '搜题']` | 练习类型 |
| `userAnswer` | `string` | `true` | | `userAnswer` | `text` | | | 用户答案 |
| `isCorrect` | `boolean` | `true` | | `isCorrect` | `boolean` | | | 是否正确 |
| `timeSpent` | `number` | `false` | | `timeSpent` | `int` | `0` | | 答题耗时(秒) |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 学习记录
 */
@Entity('study_record')
export class StudyRecordEntity extends BaseEntity {
  @Index()
  @Column({ comment: '用户ID', type: 'int' })
  userId: number;

  @Index()
  @Column({ comment: '科目ID', type: 'int' })
  subjectId: number;

  @Index()
  @Column({ comment: '题目ID', type: 'int' })
  questionId: number;

  @Column({
    comment: '练习类型',
    type: 'tinyint',
    dict: ['顺序', '题型', '错题', '随机', '收藏', '搜题']
  })
  practiceType: number;

  @Column({ comment: '用户答案', type: 'text', nullable: true })
  userAnswer: string;

  @Column({ comment: '是否正确', type: 'boolean', nullable: true })
  isCorrect: boolean;

  @Column({ comment: '答题耗时(秒)', type: 'int', default: 0 })
  timeSpent: number;
}
```

### 错题记录 (WrongQuestion Entity)

**实体说明**: 存储用户的错题记录。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `userId` | `number` | `false` | `普通索引` | `userId` | `int` | | | 用户ID |
| `subjectId` | `number` | `false` | `普通索引` | `subjectId` | `int` | | | 科目ID |
| `questionId` | `number` | `false` | `普通索引` | `questionId` | `int` | | | 题目ID |
| `wrongCount` | `number` | `false` | | `wrongCount` | `int` | `1` | | 错误次数 |
| `lastWrongAt` | `Date` | `false` | | `lastWrongAt` | `datetime` | 当前时间 | | 最后一次错误时间 |
| `memo` | `string` | `true` | | `memo` | `text` | | | 备注 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |
| `updateTime` | `Date` | `false` | | `updateTime` | `datetime` | 当前时间 | | 更新时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 错题记录
 */
@Entity('wrong_question')
export class WrongQuestionEntity extends BaseEntity {
  @Index()
  @Column({ comment: '用户ID', type: 'int' })
  userId: number;

  @Index()
  @Column({ comment: '科目ID', type: 'int' })
  subjectId: number;

  @Index()
  @Column({ comment: '题目ID', type: 'int' })
  questionId: number;

  @Column({ comment: '错误次数', type: 'int', default: 1 })
  wrongCount: number;

  @Column({ comment: '最后一次错误时间', type: 'datetime' })
  lastWrongAt: Date;

  @Column({ comment: '备注', type: 'text', nullable: true })
  memo: string;
}
```

### 收藏题目 (FavoriteQuestion Entity)

**实体说明**: 存储用户收藏的题目信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `userId` | `number` | `false` | `普通索引` | `userId` | `int` | | | 用户ID |
| `subjectId` | `number` | `false` | `普通索引` | `subjectId` | `int` | | | 科目ID |
| `questionId` | `number` | `false` | `普通索引` | `questionId` | `int` | | | 题目ID |
| `memo` | `string` | `true` | | `memo` | `text` | | | 备注 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 收藏题目
 */
@Entity('favorite_question')
export class FavoriteQuestionEntity extends BaseEntity {
  @Index()
  @Column({ comment: '用户ID', type: 'int' })
  userId: number;

  @Index()
  @Column({ comment: '科目ID', type: 'int' })
  subjectId: number;

  @Index()
  @Column({ comment: '题目ID', type: 'int' })
  questionId: number;

  @Column({ comment: '备注', type: 'text', nullable: true })
  memo: string;
}
```

### 考试计划 (Exam Entity)

**实体说明**: 存储科目的考试计划信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `subjectId` | `number` | `false` | `普通索引` | `subjectId` | `int` | | | 科目ID |
| `name` | `string` | `false` | | `name` | `varchar(100)` | | | 考试名称 |
| `description` | `string` | `true` | | `description` | `text` | | | 描述 |
| `questionCount` | `number` | `false` | | `questionCount` | `int` | | | 题目数量 |
| `singleCount` | `number` | `false` | | `singleCount` | `int` | `0` | | 单选题数量 |
| `multipleCount` | `number` | `false` | | `multipleCount` | `int` | `0` | | 多选题数量 |
| `judgeCount` | `number` | `false` | | `judgeCount` | `int` | `0` | | 判断题数量 |
| `passingScore` | `number` | `false` | | `passingScore` | `int` | `60` | | 及格分数 |
| `totalScore` | `number` | `false` | | `totalScore` | `int` | `100` | | 总分 |
| `timeLimit` | `number` | `false` | | `timeLimit` | `int` | `60` | | 时间限制(分钟) |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['未发布', '已发布', '已结束']` | 状态 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |
| `updateTime` | `Date` | `false` | | `updateTime` | `datetime` | 当前时间 | | 更新时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 考试计划
 */
@Entity('exam')
export class ExamEntity extends BaseEntity {
  @Index()
  @Column({ comment: '科目ID', type: 'int' })
  subjectId: number;

  @Column({ comment: '考试名称', length: 100 })
  name: string;

  @Column({ comment: '描述', type: 'text', nullable: true })
  description: string;

  @Column({ comment: '题目数量', type: 'int' })
  questionCount: number;

  @Column({ comment: '单选题数量', type: 'int', default: 0 })
  singleCount: number;

  @Column({ comment: '多选题数量', type: 'int', default: 0 })
  multipleCount: number;

  @Column({ comment: '判断题数量', type: 'int', default: 0 })
  judgeCount: number;

  @Column({ comment: '及格分数', type: 'int', default: 60 })
  passingScore: number;

  @Column({ comment: '总分', type: 'int', default: 100 })
  totalScore: number;

  @Column({ comment: '时间限制(分钟)', type: 'int', default: 60 })
  timeLimit: number;

  @Column({
    comment: '状态',
    type: 'tinyint',
    default: 0,
    dict: ['未发布', '已发布', '已结束']
  })
  status: number;
}
```

### 考试记录 (ExamRecord Entity)

**实体说明**: 存储用户的考试记录信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `userId` | `number` | `false` | `普通索引` | `userId` | `int` | | | 用户ID |
| `subjectId` | `number` | `false` | `普通索引` | `subjectId` | `int` | | | 科目ID |
| `examId` | `number` | `false` | `普通索引` | `examId` | `int` | | | 考试ID |
| `score` | `number` | `false` | | `score` | `int` | `0` | | 得分 |
| `isPassed` | `boolean` | `false` | | `isPassed` | `boolean` | `false` | | 是否通过 |
| `usedTime` | `number` | `false` | | `usedTime` | `int` | | | 用时(秒) |
| `answers` | `string` | `true` | | `answers` | `json` | | | 答案JSON |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['进行中', '已完成', '已超时']` | 状态 |
| `startTime` | `Date` | `false` | | `startTime` | `datetime` | 当前时间 | | 开始时间 |
| `endTime` | `Date` | `true` | | `endTime` | `datetime` | | | 结束时间 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 考试记录
 */
@Entity('exam_record')
export class ExamRecordEntity extends BaseEntity {
  @Index()
  @Column({ comment: '用户ID', type: 'int' })
  userId: number;

  @Index()
  @Column({ comment: '科目ID', type: 'int' })
  subjectId: number;

  @Index()
  @Column({ comment: '考试ID', type: 'int' })
  examId: number;

  @Column({ comment: '得分', type: 'int', default: 0 })
  score: number;

  @Column({ comment: '是否通过', type: 'boolean', default: false })
  isPassed: boolean;

  @Column({ comment: '用时(秒)', type: 'int' })
  usedTime: number;

  @Column({ comment: '答案JSON', type: 'json', nullable: true })
  answers: string;

  @Column({
    comment: '状态',
    type: 'tinyint',
    default: 0,
    dict: ['进行中', '已完成', '已超时']
  })
  status: number;

  @Column({ comment: '开始时间', type: 'datetime' })
  startTime: Date;

  @Column({ comment: '结束时间', type: 'datetime', nullable: true })
  endTime: Date;
}
```

## 结语

本文档已完全遵循 Cool-Admin 8.x 和 TypeORM 0.3.20 的规范，移除了所有 Prisma 特有的关系映射语法，如 `@relation`、`@OneToMany` 或 `@ManyToOne` 等。实现了通过 typeorm 表字段的直接关联。

在 Cool-Admin 中使用这些实体时，请注意:

1. 所有实体应继承自 `BaseEntity`，导入路径为 `../../base/entity/base`
2. 字段命名采用驼峰式，如 `lastWrongAt`、`passingScore`
3. 枚举值通过 Cool-Admin 的 `dict` 属性进行定义
4. 表关联通过外键字段 (`xxxId`) 进行实现，不使用 TypeORM 的关系装饰器
5. 所有实体应添加适当的索引，尤其是查询频繁的外键字段