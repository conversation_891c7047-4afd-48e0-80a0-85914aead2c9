# 考理论教育平台 - 数据模型文档

## 核心实体关系

基于 Cool-Admin 8.x 和 TypeORM 0.3.20 设计的无外键关系数据模型 (通过 ID 字段关联):

```
地区(Region)
    ↑ regionId
    ├─────── 用户.cityId/provinceId (User) 
    └─────── 科目.provinceId (Subject)

用户(User)
    ↑ userId
    ├─────── 订单(Order) ─── subjectId ──→ 科目(Subject)
    ├─────── 学习记录(StudyRecord) ─── questionId ──→ 题目(Question)
    ├─────── 考试记录(ExamRecord) ─── examId ──→ 考试(Exam)
    ├─────── 错题记录(WrongQuestion) ─── questionId ──→ 题目(Question)
    ├─────── 收藏题目(FavoriteQuestion) ─── questionId ──→ 题目(Question)
    └─────── 分销商(Distributor)

科目(Subject)
    ↑ subjectId
    ├─────── 题目(Question) ─── questionId ──→ 选项(QuestionOption)
    ├─────── 考试(Exam)
    └─────── 价格方案(SubjectPrice)
```

## TypeORM 实体说明

所有实体均继承自 `BaseEntity`，位于 `src/modules/base/entity/base.ts`。BaseEntity 已经包含以下字段：
- id: 主键，自增
- createTime: 创建时间
- updateTime: 更新时间

数据库设计遵循 Cool-Admin 8.x 规范：
- 不使用外键关系，如 @ManyToOne、@OneToMany 等
- 使用 dict 属性定义枚举值
- 实体字段使用驼峰命名
- TypeORM 版本 0.3.20

## 用户体系模型

### 用户基础信息 (User Entity)

**实体说明**: 存储平台用户的基本信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `nickname` | `string` | `true` | `普通索引` | `nickname` | `varchar(50)` | | | 昵称 |
| `avatar` | `string` | `true` | | `avatar` | `varchar(500)` | | | 头像URL |
| `phone` | `string` | `true` | `唯一索引` | `phone` | `varchar(20)` | | | 手机号码 |
| `cityId` | `number` | `true` | `普通索引` | `cityId` | `int` | | | 城市ID(关联地区表) |
| `provinceId` | `number` | `true` | `普通索引` | `provinceId` | `int` | | | 省份ID(关联地区表) |
| `city` | `string` | `true` | | `city` | `varchar(50)` | | | 城市名称(缓存) |
| `province` | `string` | `true` | | `province` | `varchar(50)` | | | 省份名称(缓存) |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['正常', '禁用']` | 状态 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |
| `updateTime` | `Date` | `false` | | `updateTime` | `datetime` | 当前时间 | | 更新时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 用户基础信息
 */
@Entity('user')
export class UserEntity extends BaseEntity {
  @Index()
  @Column({ comment: '昵称', nullable: true, length: 50 })
  nickname: string;

  @Column({ comment: '头像URL', nullable: true, length: 500 })
  avatar: string;

  @Index({ unique: true })
  @Column({ comment: '手机号码', nullable: true, length: 20 })
  phone: string;
  
  @Index()
  @Column({ comment: '城市ID', nullable: true, type: 'int' })
  cityId: number;
  
  @Index()
  @Column({ comment: '省份ID', nullable: true, type: 'int' })
  provinceId: number;

  @Column({ comment: '城市名称', nullable: true, length: 50 })
  city: string;

  @Column({ comment: '省份名称', nullable: true, length: 50 })
  province: string;

  @Column({ 
    comment: '状态', 
    default: 0,
    type: 'tinyint',
    dict: ['正常', '禁用']
  })
  status: number;
}
```



**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 微信账号信息 (WechatAccount Entity)

**实体说明**: 存储用户绑定的微信账号信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `userId` | `number` | `false` | `普通索引` | `userId` | `int` | | | 关联的用户ID |
| `unionId` | `string` | `true` | `唯一索引` | `unionId` | `varchar(100)` | | | 微信 UnionID |
| `openId` | `string` | `false` | `唯一索引` | `openId` | `varchar(100)` | | | 微信 OpenID |
| `platform` | `number` | `false` | | `platform` | `tinyint` | | `dict: ['小程序', '公众号']` | 平台 |
| `nickname` | `string` | `true` | | `nickname` | `varchar(50)` | | | 微信昵称 |
| `avatar` | `string` | `true` | | `avatar` | `varchar(500)` | | | 微信头像URL |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 微信账号信息
 */
@Entity('wechat_account')
export class WechatAccountEntity extends BaseEntity {
  @Index()
  @Column({ comment: '关联的用户ID', type: 'int' })
  userId: number;

  @Index({ unique: true })
  @Column({ comment: '微信 UnionID', nullable: true, length: 100 })
  unionId: string;

  @Index({ unique: true })
  @Column({ comment: '微信 OpenID', length: 100 })
  openId: string;

  @Column({ 
    comment: '平台', 
    type: 'tinyint',
    dict: ['小程序', '公众号']
  })
  platform: number;

  @Column({ comment: '微信昵称', nullable: true, length: 50 })
  nickname: string;

  @Column({ comment: '微信头像URL', nullable: true, length: 500 })
  avatar: string;
}
```

**移除的 Prisma 关系字段**:
* `wechatAccounts: WechatAccount[]`
* `orders: Order[]`
* `studyRecords: StudyRecord[]`
* `examRecords: ExamRecord[]`
* `wrongQuestions: WrongQuestion[]`
* `favoriteQuestions: FavoriteQuestion[]`
* `distributor: Distributor?`

**TypeORM 实体基类**: `import { BaseEntity } from '../../base/entity/base';` (根据项目规范)

### 分销商信息 (Distributor Entity)

**实体说明**: 存储分销商的详细信息，包括层级、佣金、收益等。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `userId` | `number` | `false` | `唯一索引` | `userId` | `int` | | | 关联的用户ID |
| `parentId` | `number` | `true` | `普通索引` | `parentId` | `int` | | | 上级分销商ID |
| `level` | `number` | `false` | | `level` | `tinyint` | `1` | | 分销层级 |
| `commission` | `number` | `false` | | `commission` | `decimal(5,4)` | `0.15` | | 佣金比例 |
| `totalEarned` | `number` | `false` | | `totalEarned` | `decimal(10,2)` | `0` | | 累计收益 |
| `balance` | `number` | `false` | | `balance` | `decimal(10,2)` | `0` | | 可提现余额 |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['正常', '禁用']` | 状态 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |
| `updateTime` | `Date` | `false` | | `updateTime` | `datetime` | 当前时间 | | 更新时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 分销商信息
 */
@Entity('distributor')
export class DistributorEntity extends BaseEntity {
  @Index({ unique: true })
  @Column({ comment: '关联的用户ID', type: 'int' })
  userId: number;

  @Index()
  @Column({ comment: '上级分销商ID', type: 'int', nullable: true })
  parentId: number;

  @Column({ comment: '分销层级', type: 'tinyint', default: 1 })
  level: number;

  @Column({ comment: '佣金比例', type: 'decimal', precision: 5, scale: 4, default: 0.15 })
  commission: number;

  @Column({ comment: '累计收益', type: 'decimal', precision: 10, scale: 2, default: 0 })
  totalEarned: number;

  @Column({ comment: '可提现余额', type: 'decimal', precision: 10, scale: 2, default: 0 })
  balance: number;

  @Column({ 
    comment: '状态', 
    type: 'tinyint',
    default: 0,
    dict: ['正常', '禁用']
  })
  status: number;
}
```

## 内容体系模型

### 地区信息 (Region Entity)

**实体说明**: 存储地理位置信息，包括省份、直辖市和城市，用于本地化内容展示。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `name` | `string` | `false` | | `name` | `varchar(50)` | | | 地区名称 |
| `parentId` | `number` | `true` | `普通索引` | `parentId` | `int` | | | 父级地区ID |
| `type` | `number` | `false` | | `type` | `tinyint` | `2` | `dict: ['省份', '直辖市', '城市', '区县']` | 地区类型 |
| `code` | `string` | `true` | | `code` | `varchar(20)` | | | 地区编码 |
| `orderNum` | `number` | `false` | | `orderNum` | `int` | `0` | | 排序号 |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['正常', '禁用']` | 状态 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |
| `updateTime` | `Date` | `false` | | `updateTime` | `datetime` | 当前时间 | | 更新时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 地区信息
 */
@Entity('region')
export class RegionEntity extends BaseEntity {
  @Column({ comment: '地区名称', length: 50 })
  name: string;

  @Index()
  @Column({ comment: '父级地区ID', type: 'int', nullable: true })
  parentId: number;

  @Column({ 
    comment: '地区类型', 
    type: 'tinyint', 
    default: 2,
    dict: ['省份', '直辖市', '城市', '区县'] 
  })
  type: number;

  @Column({ comment: '地区编码', length: 20, nullable: true })
  code: string;

  @Column({ comment: '排序号', type: 'int', default: 0 })
  orderNum: number;

  @Column({ 
    comment: '状态', 
    type: 'tinyint',
    default: 0,
    dict: ['正常', '禁用']
  })
  status: number;
}
```

### 分类信息 (Category Entity)

**实体说明**: 存储内容分类的层级结构信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `name` | `string` | `false` | | `name` | `varchar(100)` | | | 分类名称 |
| `parentId` | `number` | `true` | `普通索引` | `parentId` | `int` | | | 父分类ID |
| `level` | `number` | `false` | | `level` | `tinyint` | `1` | | 层级 |
| `orderNum` | `number` | `false` | | `orderNum` | `int` | `0` | | 排序号 |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['正常', '禁用']` | 状态 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 分类信息
 */
@Entity('category')
export class CategoryEntity extends BaseEntity {
  @Column({ comment: '分类名称', length: 100 })
  name: string;

  @Index()
  @Column({ comment: '父分类ID', type: 'int', nullable: true })
  parentId: number;

  @Column({ comment: '层级', type: 'tinyint', default: 1 })
  level: number;

  @Column({ comment: '排序号', type: 'int', default: 0 })
  orderNum: number;

  @Column({ 
    comment: '状态', 
    type: 'tinyint',
    default: 0,
    dict: ['正常', '禁用']
  })
  status: number;
}
```

### 科目信息 (Subject Entity)

**实体说明**: 存储考试科目的基本信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `categoryId` | `number` | `false` | `普通索引` | `categoryId` | `int` | | | 分类ID |
| `name` | `string` | `false` | | `name` | `varchar(100)` | | | 科目名称 |
| `displayTemplate` | `string` | `true` | | `displayTemplate` | `varchar(200)` | | | 显示模板(支持地域变量) |
| `provinceId` | `number` | `true` | `普通索引` | `provinceId` | `int` | | | 省份ID(关联地区表) |
| `description` | `string` | `true` | | `description` | `text` | | | 描述 |
| `coverUrl` | `string` | `true` | | `coverUrl` | `varchar(500)` | | | 封面图片URL |
| `questionCount` | `number` | `false` | | `questionCount` | `int` | `0` | | 题目总数量 |
| `singleCount` | `number` | `false` | | `singleCount` | `int` | `0` | | 单选题数量 |
| `multipleCount` | `number` | `false` | | `multipleCount` | `int` | `0` | | 多选题数量 |
| `judgeCount` | `number` | `false` | | `judgeCount` | `int` | `0` | | 判断题数量 |
| `validDays` | `number` | `false` | | `validDays` | `int` | `365` | | 有效期(天) |
| `price` | `number` | `false` | | `price` | `decimal` | `0.00` | | 价格 |
| `isHot` | `boolean` | `false` | | `isHot` | `boolean` | `false` | | 是否热门 |
| `isRecommend` | `boolean` | `false` | | `isRecommend` | `boolean` | `false` | | 是否推荐 |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['未上架', '已上架', '已下架']` | 状态 |
| `orderNum` | `number` | `false` | | `orderNum` | `int` | `0` | | 排序号 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |
| `updateTime` | `Date` | `false` | | `updateTime` | `datetime` | 当前时间 | | 更新时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 科目信息
 */
@Entity('subject')
export class SubjectEntity extends BaseEntity {
  @Index()
  @Column({ comment: '分类ID', type: 'int' })
  categoryId: number;

  @Column({ comment: '科目名称', length: 100 })
  name: string;
  
  @Column({ comment: '显示模板(支持地域变量)', length: 200, nullable: true })
  displayTemplate: string;
  
  @Index()
  @Column({ comment: '省份ID(关联地区表)', type: 'int', nullable: true })
  provinceId: number;

  @Column({ comment: '描述', type: 'text', nullable: true })
  description: string;

  @Column({ comment: '封面图片URL', length: 500, nullable: true })
  coverUrl: string;

  @Column({ comment: '题目总数量', type: 'int', default: 0 })
  questionCount: number;

  @Column({ comment: '单选题数量', type: 'int', default: 0 })
  singleCount: number;

  @Column({ comment: '多选题数量', type: 'int', default: 0 })
  multipleCount: number;

  @Column({ comment: '判断题数量', type: 'int', default: 0 })
  judgeCount: number;

  @Column({ comment: '有效期(天)', type: 'int', default: 365 })
  validDays: number;

  @Column({ comment: '价格', type: 'decimal', precision: 10, scale: 2, default: 0 })
  price: number;

  @Column({ comment: '是否热门', type: 'boolean', default: false })
  isHot: boolean;

  @Column({ comment: '是否推荐', type: 'boolean', default: false })
  isRecommend: boolean;

  @Column({
    comment: '状态',
    type: 'tinyint',
    default: 0,
    dict: ['未上架', '已上架', '已下架']
  })
  status: number;

  @Column({ comment: '排序号', type: 'int', default: 0 })
  orderNum: number;
}
```

### 科目价格方案 (SubjectPrice Entity)

**实体说明**: 存储科目的不同价格套餐方案。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `subjectId` | `number` | `false` | `普通索引` | `subjectId` | `int` | | | 关联的科目ID |
| `name` | `string` | `false` | | `name` | `varchar(100)` | | | 套餐名称 |
| `price` | `number` | `false` | | `price` | `decimal(10,2)` | | | 价格 |
| `validDays` | `number` | `false` | | `validDays` | `int` | | | 有效天数 |
| `isDefault` | `boolean` | `false` | | `isDefault` | `boolean` | `false` | | 是否默认套餐 |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['正常', '禁用']` | 状态 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 科目价格方案
 */
@Entity('subject_price')
export class SubjectPriceEntity extends BaseEntity {
  @Index()
  @Column({ comment: '关联的科目ID', type: 'int' })
  subjectId: number;

  @Column({ comment: '套餐名称', length: 100 })
  name: string;

  @Column({ comment: '价格', type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @Column({ comment: '有效天数', type: 'int' })
  validDays: number;

  @Column({ comment: '是否默认套餐', type: 'boolean', default: false })
  isDefault: boolean;

  @Column({ 
    comment: '状态', 
    type: 'tinyint',
    default: 0,
    dict: ['正常', '禁用']
  })
  status: number;
}
```

### 题目信息 (Question Entity)

**实体说明**: 存储题库中的题目信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `subjectId` | `number` | `false` | `普通索引` | `subjectId` | `int` | | | 所属科目ID |
| `type` | `number` | `false` | | `type` | `tinyint` | | `dict: ['单选', '多选', '判断']` | 题目类型 |
| `content` | `string` | `false` | | `content` | `text` | | | 题目内容 |
| `analysis` | `string` | `true` | | `analysis` | `text` | | | 题目解析 |
| `difficulty` | `number` | `false` | | `difficulty` | `tinyint` | `0` | `dict: ['简单', '中等', '困难']` | 难度级别 |
| `orderNum` | `number` | `false` | | `orderNum` | `int` | `0` | | 排序号 |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['正常', '禁用']` | 状态 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |
| `updateTime` | `Date` | `false` | | `updateTime` | `datetime` | 当前时间 | | 更新时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 题目信息
 */
@Entity('question')
export class QuestionEntity extends BaseEntity {
  @Index()
  @Column({ comment: '所属科目ID', type: 'int' })
  subjectId: number;

  @Column({
    comment: '题目类型',
    type: 'tinyint',
    dict: ['单选', '多选', '判断']
  })
  type: number;

  @Column({ comment: '题目内容', type: 'text' })
  content: string;

  @Column({ comment: '题目解析', type: 'text', nullable: true })
  analysis: string;

  @Column({
    comment: '难度级别',
    type: 'tinyint',
    default: 0,
    dict: ['简单', '中等', '困难']
  })
  difficulty: number;
  
  @Column({ comment: '排序号', type: 'int', default: 0 })
  orderNum: number;

  @Column({ 
    comment: '状态', 
    type: 'tinyint',
    default: 0,
    dict: ['正常', '禁用']
  })
  status: number;
}
```

### 题目选项 (QuestionOption Entity)

**实体说明**: 存储题目的选项信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `questionId` | `number` | `false` | `普通索引` | `questionId` | `int` | | | 关联的题目ID |
| `content` | `string` | `false` | | `content` | `text` | | | 选项内容 |
| `isCorrect` | `boolean` | `false` | | `isCorrect` | `boolean` | `false` | | 是否为正确选项 |
| `orderNum` | `number` | `false` | | `orderNum` | `int` | `0` | | 排序号 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 题目选项
 */
@Entity('question_option')
export class QuestionOptionEntity extends BaseEntity {
  @Index()
  @Column({ comment: '关联的题目ID', type: 'int' })
  questionId: number;

  @Column({ comment: '选项内容', type: 'text' })
  content: string;

  @Column({ comment: '是否为正确选项', type: 'boolean', default: false })
  isCorrect: boolean;

  @Column({ comment: '排序号', type: 'int', default: 0 })
  orderNum: number;
}
```

## 交易体系模型

### 订单信息 (Order Entity)

**实体说明**: 存储用户购买科目的订单信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `orderNo` | `string` | `false` | `唯一索引` | `orderNo` | `varchar(32)` | | | 订单编号 |
| `userId` | `number` | `false` | `普通索引` | `userId` | `int` | | | 用户ID |
| `subjectId` | `number` | `false` | `普通索引` | `subjectId` | `int` | | | 科目ID |
| `priceId` | `number` | `false` | `普通索引` | `priceId` | `int` | | | 价格方案ID |
| `amount` | `number` | `false` | | `amount` | `decimal(10,2)` | | | 订单金额 |
| `payAmount` | `number` | `false` | | `payAmount` | `decimal(10,2)` | `0` | | 实际支付金额 |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['待支付', '已支付', '已取消', '异常']` | 订单状态 |
| `validUntil` | `Date` | `true` | | `validUntil` | `datetime` | | | 权限有效期限 |
| `distributorId` | `number` | `true` | `普通索引` | `distributorId` | `int` | | | 推广人ID |
| `paidAt` | `Date` | `true` | | `paidAt` | `datetime` | | | 支付时间 |
| `createTime` | `Date` | `false` | | `createTime` | `datetime` | 当前时间 | | 创建时间 |
| `updateTime` | `Date` | `false` | | `updateTime` | `datetime` | 当前时间 | | 更新时间 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 订单信息
 */
@Entity('order')
export class OrderEntity extends BaseEntity {
  @Index({ unique: true })
  @Column({ comment: '订单编号', length: 32 })
  orderNo: string;

  @Index()
  @Column({ comment: '用户ID', type: 'int' })
  userId: number;

  @Index()
  @Column({ comment: '科目ID', type: 'int' })
  subjectId: number;

  @Index()
  @Column({ comment: '价格方案ID', type: 'int' })
  priceId: number;

  @Column({ comment: '订单金额', type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ comment: '实际支付金额', type: 'decimal', precision: 10, scale: 2, default: 0 })
  payAmount: number;

  @Column({
    comment: '订单状态',
    type: 'tinyint',
    default: 0,
    dict: ['待支付', '已支付', '已取消', '异常']
  })
  status: number;

  @Column({ comment: '权限有效期限', type: 'datetime', nullable: true })
  validUntil: Date;

  @Index()
  @Column({ comment: '推广人ID', type: 'int', nullable: true })
  distributorId: number;

  @Column({ comment: '支付时间', type: 'datetime', nullable: true })
  paidAt: Date;
}
```

### 支付记录 (PaymentRecord Entity)

**实体说明**: 存储订单的支付记录信息。

| 属性名 | 类型 | 可空 | 索引 | 数据库列名 | 数据库类型 | 默认值 | CoolAdmin Dict/Options | 注释 |
|-------|------|------|------|-----------|-----------|--------|------------------------|------|
| `id` | `number` | `false` | `主键` | `id` | `int` | 自增 | | ID |
| `orderId` | `number` | `false` | `普通索引` | `orderId` | `int` | | | 关联的订单ID |
| `paymentType` | `number` | `false` | | `paymentType` | `tinyint` | | `dict: ['微信支付', '支付宝支付', '激活码']` | 支付类型 |
| `amount` | `number` | `false` | | `amount` | `decimal(10,2)` | | | 支付金额 |
| `tradeNo` | `string` | `true` | | `tradeNo` | `varchar(100)` | | | 内部交易号 |
| `thirdTradeNo` | `string` | `true` | | `thirdTradeNo` | `varchar(100)` | | | 第三方交易号 |
| `activationCode` | `string` | `true` | | `activationCode` | `varchar(50)` | | | 激活码 |
| `status` | `number` | `false` | | `status` | `tinyint` | `0` | `dict: ['待支付', '支付成功', '支付失败']` | 支付状态 |
| `paidAt` | `Date` | `true` | | `paidAt` | `datetime` | | | 支付时间 |
| `failReason` | `string` | `true` | | `failReason` | `varchar(255)` | | | 支付失败原因 |

**TypeORM 实体示例**:
```typescript
import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 支付记录
 */
@Entity('payment_record')
export class PaymentRecordEntity extends BaseEntity {
  @Index()
  @Column({ comment: '关联的订单ID', type: 'int' })
  orderId: number;

  @Column({
    comment: '支付类型',
    type: 'tinyint',
    dict: ['微信支付', '支付宝支付', '激活码']
  })
  paymentType: number;

  @Column({ comment: '支付金额', type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ comment: '内部交易号', length: 100, nullable: true })
  tradeNo: string;

  @Column({ comment: '第三方交易号', length: 100, nullable: true })
  thirdTradeNo: string;

  @Column({ comment: '激活码', length: 50, nullable: true })
  activationCode: string;

  @Column({
    comment: '支付状态',
    type: 'tinyint',
    default: 0,
    dict: ['待支付', '支付成功', '支付失败']
  })
  status: number;

  @Column({ comment: '支付时间', type: 'datetime', nullable: true })
  paidAt: Date;

  @Column({ comment: '支付失败原因', length: 255, nullable: true })
  failReason: string;
}
```

## 结语

本文档已完全遵循 Cool-Admin 8.x 和 TypeORM 0.3.20 的规范，移除了所有外键关系映射装饰器，如 `@OneToMany` 或 `@ManyToOne` 等。实现了通过 typeorm 表字段的直接关联。

在 Cool-Admin 中使用这些实体时，请注意:

1. 所有实体应继承自 `BaseEntity`，导入路径为 `../../base/entity/base`
2. 字段命名采用驼峰式，如 `lastWrongAt`、`passingScore`
3. 枚举值通过 Cool-Admin 的 `dict` 属性进行定义
4. 表关联通过外键字段 (`xxxId`) 进行实现，不使用 TypeORM 的关系装饰器
5. 所有实体应添加适当的索引，尤其是查询频繁的外键字段

## 地域化内容显示实现

### 实现方案

本次设计新增了地区(Region)实体，并对用户(User)和科目(Subject)实体进行了修改，主要用于解决基于地域显示不同内容的需求。实现方案如下：

1. **地区信息结构**：Region实体设计为树状结构，通过parentId字段建立层级关系，支持省份-城市-区县的层级组织。

2. **用户地区关联**：User实体新增cityId和provinceId字段，用于记录用户所属的城市和省份，同时保留city和province字段作为冗余存储，避免频繁联表查询。

3. **科目地区关联**：Subject实体新增provinceId和displayTemplate字段，其中：
   - provinceId：标识该科目归属的省份
   - displayTemplate：提供一个支持变量的模板字符串，用于根据用户所在地区动态生成科目显示名称

### 显示模板使用示例

Subject.displayTemplate 支持使用变量来动态组装显示内容，例如：

```
// 模板示例
"{province}驾考科目一理论考试"

// 当上海用户访问时，显示为：
"上海驾考科目一理论考试"

// 当安徽用户访问时，显示为：
"安徽驾考科目一理论考试" 
```

### 业务实现建议

1. **地区数据初始化**：系统初始化时，预先导入全国各省市区县数据到Region表。

2. **用户注册/登录流程**：在用户首次使用系统时，引导用户选择或自动检测所在地区，并更新User表的cityId、provinceId字段。

3. **科目展示处理**：
   - 普通科目：provinceId为空，面向全国用户
   - 地域专属科目：设置了provinceId，只对特定省份用户展示
   - 地域内容展示：使用Service层处理displayTemplate，根据当前用户地区信息替换模板变量

4. **题库管理**：可以考虑在Question实体中也增加provinceId字段，用于管理地域特定的题目。

这种设计方案既能满足地域化内容展示的需求，又保持了数据模型的清晰和高效。