# 考理论教育平台 - 技术架构文档

## 整体架构
采用 **Vue Shop Vite + Node.js 全栈架构**，支持多端接入：

```
┌─────────────────────────────────────────────────────────────┐
│                        Nginx + 负载均衡                      │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┼───────────────────────────────────────┐
│                     │           前端层                       │
│  ┌─────────────────┐│┌─────────────────┐ ┌─────────────────┐ │
│  │   微信小程序     │││   微信公众号     │ │    管理后台      │ │
│  │  (Taro + Vue)   │││   (Vue 3 H5)    │ │ (Vue Shop Vite) │ │
│  └─────────────────┘│└─────────────────┘ └─────────────────┘ │
│  ┌─────────────────┐│┌─────────────────┐ ┌─────────────────┐ │
│  │   H5/Web端      │││   移动端PWA     │ │   PC官网        │ │
│  │   (Vue 3)       │││   (Vue 3)       │ │   (Vue 3)       │ │
│  └─────────────────┘│└─────────────────┘ └─────────────────┘ │
└─────────────────────┼───────────────────────────────────────┘
                      │
┌─────────────────────┼───────────────────────────────────────┐
│                     │           应用层                       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Node.js + Express API                     │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │ │
│  │  │  用户服务    │ │  内容服务    │ │     支付服务        │ │ │
│  │  └─────────────┘ └─────────────┘ └─────────────────────┘ │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │ │
│  │  │ 微信接入服务 │ │  消息推送    │ │    数据分析服务      │ │ │
│  │  └─────────────┘ └─────────────┘ └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────┼───────────────────────────────────────┘
                      │
┌─────────────────────┼───────────────────────────────────────┐
│                     │           数据层                       │
│  ┌─────────────────┐│┌─────────────────┐ ┌─────────────────┐ │
│  │   PostgreSQL    │││      Redis      │ │   本地存储       │ │
│  │   (主数据库)     │││     (缓存)      │ │  (文件存储)      │ │
│  └─────────────────┘│└─────────────────┘ └─────────────────┘ │
│                     │                                       │
└─────────────────────┼───────────────────────────────────────┘
```

## 技术选型

### 管理后台技术栈 (Vue Shop Vite 2025)

| 技术层 | 技术选择 | 版本 | 理由 |
|--------|----------|------|------|
| **核心框架** | Vue Shop Vite | 2025付费版 | vue-admin-better团队出品，40+高质量单页 |
| **JavaScript框架** | Vue | 3.x | 渐进式框架，Composition API，学习成本低 |
| **构建工具** | Vite | 5.x | 极速构建，热更新快，开发体验佳 |
| **UI组件库** | Element Plus | 2.x | Vue生态最成熟的UI库，组件丰富 |
| **开发语言** | TypeScript | 5.x | 类型安全，提升代码质量 |
| **状态管理** | Pinia | 2.x | Vue官方推荐，简单易用 |
| **路由管理** | Vue Router | 4.x | Vue官方路由，功能完善 |
| **样式解决方案** | SCSS + CSS Variables | - | 灵活的样式管理 |
| **图标库** | Element Plus Icons | - | 丰富的图标资源 |
| **测试框架** | Vitest | - | Vite原生测试框架 |
| **代码规范** | ESLint + Prettier | - | 代码质量保证 |
| **数据模拟** | Mock.js | - | 开发阶段数据模拟 |

### 用户端技术栈 (Vue 3)

| 技术层 | 技术选择 | 版本 | 理由 |
|--------|----------|------|------|
| **前端框架** | Vue | 3.x | 与管理后台技术栈统一 |
| **构建工具** | Vite | 5.x | 极速构建，开发体验佳 |
| **小程序框架** | Taro | 3.x | 支持Vue，多端统一开发 |
| **UI组件库** | Element Plus + Taro UI | 2.x | 移动端和PC端组件统一 |
| **状态管理** | Pinia | 2.x | 轻量级状态管理 |
| **样式方案** | SCSS + UnoCSS | - | 原子化CSS，开发效率高 |
| **表单处理** | Element Plus Form | - | 完整的表单解决方案 |
| **数据验证** | Yup / Joi | - | 强大的数据验证库 |

### 后端服务技术栈

| 技术层 | 技术选择 | 版本 | 理由 |
|--------|----------|------|------|
| **后端框架** | Express.js | 4.x | 简单易用，AI开发友好，生态丰富 |
| **运行时** | Node.js | 18+ | JavaScript统一技术栈 |
| **数据库** | PostgreSQL | 15+ | 功能强大，JSON支持好，性能优秀 |
| **ORM框架** | Prisma | 5.x | 类型安全，开发体验佳，迁移管理好 |
| **缓存** | Redis | 7.x | 高性能，数据结构丰富 |
| **认证** | JWT + bcryptjs | - | 简单可靠的认证方案 |
| **文件上传** | Multer | - | Express文件上传中间件 |
| **进程管理** | PM2 | - | 生产环境进程管理 |

### 第三方集成

| 技术层 | 技术选择 | 理由 |
|--------|----------|------|
| **微信接入** | 微信开发者工具SDK | 小程序、公众号统一接入 |
| **支付集成** | 微信支付 + 支付宝 SDK | 官方SDK，支持微信、支付宝、激活码 |
| **消息推送** | 微信模板消息 | 学习提醒、考试通知 |
| **文件上传** | 云存储 SDK | 阿里云OSS/腾讯云COS |
| **任务队列** | Bull Queue + Redis | 处理异步任务，如订单超时 |
| **日志系统** | Winston + 云日志 | 结构化日志，便于调试和监控 |
| **API文档** | Swagger/OpenAPI | 自动生成API文档 |
| **部署平台** | Vercel | 自动化部署，全球CDN |
| **监控告警** | Sentry + Vercel Analytics | 错误追踪，性能监控 |
| **CI/CD** | GitHub Actions | 自动化构建和部署 |
| **反向代理** | Nginx | 高性能Web服务器，负载均衡 |
| **SSL证书** | Let's Encrypt | 免费SSL证书，HTTPS支持 |

## Vue Shop Vite 2025 核心特性

### 开箱即用的功能
- **🚀 极速启动**：基于 Vite 5.x，项目秒级启动，热更新极快
- **📦 丰富模板**：40+ 高质量单页模板，覆盖常见业务场景
- **🌐 国际化**：完整的多语言解决方案
- **🎨 主题定制**：灵活的主题配置，支持暗黑模式
- **💡 最佳实践**：内置Vue 3 + Composition API最佳实践
- **📱 响应式设计**：完美适配PC、平板、手机
- **🔧 TypeScript**：全面的TypeScript支持
- **🔐 权限管理**：完整的RBAC权限控制系统
- **🛠️ 开发工具**：完善的开发工具链和调试支持
- **📊 数据可视化**：内置图表组件和数据展示方案

### 内置模板页面
```
管理后台模板页面：
├── Dashboard
│   ├── 工作台 (数据概览)
│   ├── 分析页 (数据统计)
│   └── 监控页 (系统监控)
├── 系统管理
│   ├── 用户管理
│   ├── 角色管理
│   ├── 菜单管理
│   └── 部门管理
├── 表单页面
│   ├── 基础表单
│   ├── 高级表单
│   └── 分步表单
├── 列表页面
│   ├── 基础列表
│   ├── 卡片列表
│   └── 搜索列表
├── 详情页面
│   ├── 基础详情
│   └── 高级详情
├── 结果页面
│   ├── 成功页
│   └── 失败页
├── 异常页面
│   ├── 403 (无权限)
│   ├── 404 (页面不存在)
│   └── 500 (服务器错误)
└── 用户页面
    ├── 登录页
    ├── 注册页
    └── 忘记密码页
```

### Element Plus 组件生态
- **el-table**：功能强大的数据表格，支持排序、筛选、分页
- **el-form**：完整的表单解决方案，支持验证、联动
- **el-dialog**：模态对话框，支持拖拽、全屏
- **el-drawer**：抽屉组件，适合侧边栏操作
- **el-upload**：文件上传组件，支持多种上传方式
- **el-date-picker**：日期时间选择器
- **el-select**：下拉选择器，支持搜索、多选
- **el-tree**：树形控件，支持懒加载、拖拽

## Redis缓存策略

| 缓存类型 | 缓存Key格式 | 缓存内容 | 过期策略 | 使用场景 |
|---------|------------|---------|----------|---------|
| **热门科目** | `subject:hot:[province]` | 热门科目列表 | 1小时 | 首页推荐，减轻数据库压力 |
| **科目详情** | `subject:detail:[id]` | 科目详细信息 | 30分钟 | 科目详情页，高频访问 |
| **题目列表** | `question:list:[subjectId]` | 科目题目列表 | 1小时 | 练习场景，高频访问 |
| **用户权限** | `user:permission:[userId]` | 用户已购科目权限 | 5分钟 | 权限校验，高频访问 |
| **考试试题** | `exam:questions:[examId]` | 考试题目集合 | 24小时 | 考试场景，避免重复组卷 |
| **微信Token** | `wechat:access_token` | 微信接口调用凭证 | 110分钟 | 微信API调用，官方有效期2小时 |

## 核心服务设计

### 用户服务
- 用户注册登录
- 微信账号绑定
- 用户信息管理
- 权限验证

### 内容服务
- 科目管理
- 题目管理
- 分类管理
- 本地化显示

### 支付服务
- 订单管理
- 支付处理
- 激活码管理
- 退款处理

### 微信接入服务
- Token管理
- 消息处理
- 菜单管理
- 模板消息

### 学习服务
- 学习记录
- 进度跟踪
- 智能推荐
- 考试管理

### 分销服务
- 分销商管理
- 佣金计算
- 提现处理
- 推广统计

## 安全设计

### 数据安全
- 敏感数据加密存储
- API接口鉴权
- SQL注入防护
- XSS攻击防护

### 业务安全
- 支付安全验证
- 考试防作弊
- 分销风控
- 激活码防重复使用

### 系统安全
- HTTPS强制加密
- 访问频率限制
- 异常监控告警
- 数据备份恢复
