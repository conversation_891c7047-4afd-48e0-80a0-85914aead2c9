const fs = require('fs');
const path = require('path');

// 热门城市ID列表（北上广深等一线城市和重要城市）
const hotCityIds = [
  // 北京市区（一线城市）
  103, // 朝阳区
  104, // 海淀区

  // 上海市区（一线城市）
  201, // 黄浦区
  205, // 浦东新区

  // 天津市区（直辖市）
  301, // 和平区

  // 重庆市区（直辖市）
  401, // 渝中区

  // 广东省主要城市（一线城市：广州、深圳）
  2001, // 广州市
  2003, // 深圳市
  2004, // 珠海市
  2005, // 汕头市
  2006, // 佛山市
  2007, // 东莞市

  // 其他重要省会城市和经济发达城市
  501,  // 石家庄市（河北省会）
  601,  // 太原市（山西省会）
  701,  // 呼和浩特市（内蒙古首府）
  801,  // 沈阳市（辽宁省会）
  802,  // 大连市（重要港口城市）
  901,  // 长春市（吉林省会）
  1001, // 哈尔滨市（黑龙江省会）
  1101, // 南京市（江苏省会）
  1105, // 苏州市（经济发达城市）
  1201, // 杭州市（浙江省会）
  1202, // 宁波市（重要港口城市）
  1301, // 合肥市（安徽省会）
  1401, // 福州市（福建省会）
  1402, // 厦门市（经济特区）
  1501, // 南昌市（江西省会）
  1601, // 济南市（山东省会）
  1602, // 青岛市（重要港口城市）
  1701, // 郑州市（河南省会）
  1801, // 武汉市（湖北省会）
  1901, // 长沙市（湖南省会）
  2101, // 南宁市（广西首府）
  2103, // 桂林市（旅游城市）
  2201, // 海口市（海南省会）
  2202, // 三亚市（旅游城市）
  2301, // 成都市（四川省会）
  2401, // 贵阳市（贵州省会）
  2501, // 昆明市（云南省会）
  2601, // 拉萨市（西藏首府）
  2701, // 西安市（陕西省会）
  2801, // 兰州市（甘肃省会）
  2901, // 西宁市（青海省会）
  3001, // 银川市（宁夏首府）
  3101, // 乌鲁木齐市（新疆首府）
];

function updateHotCities() {
  try {
    // 读取db.json文件
    const dbPath = path.join(__dirname, '../src/modules/region/db.json');
    const dbContent = fs.readFileSync(dbPath, 'utf8');
    const dbData = JSON.parse(dbContent);

    console.log('开始更新热门城市标记...');
    console.log(`总共需要设置 ${hotCityIds.length} 个热门城市`);

    let updatedCount = 0;

    // 遍历所有地区数据
    dbData.region.forEach(region => {
      // 如果是城市类型(type=2)且在热门城市列表中
      if (region.type === 2 && hotCityIds.includes(region.id)) {
        region.isHot = 1;
        updatedCount++;
        console.log(`✅ 设置热门城市: ${region.name} (ID: ${region.id})`);
      } else if (region.type === 2) {
        // 确保其他城市的isHot字段为0
        region.isHot = region.isHot || 0;
      }
    });

    // 写回文件
    const updatedContent = JSON.stringify(dbData, null, 2);
    fs.writeFileSync(dbPath, updatedContent, 'utf8');

    console.log(`\n🎉 更新完成！`);
    console.log(`✅ 成功设置了 ${updatedCount} 个热门城市`);
    console.log(`📁 文件已保存: ${dbPath}`);

  } catch (error) {
    console.error('❌ 更新失败:', error.message);
    process.exit(1);
  }
}

// 执行更新
updateHotCities();
