# Swagger UI Dist
[![NPM version](https://badge.fury.io/js/swagger-ui-dist.svg)](http://badge.fury.io/js/swagger-ui-dist)

# API

This module, `swagger-ui-dist`, exposes Swagger-UI's entire dist folder as a dependency-free npm module.
Use `swagger-ui` instead, if you'd like to have npm install dependencies for you.

`SwaggerUIBundle` and `SwaggerUIStandalonePreset` can be imported:
```javascript
  import { SwaggerUIBundle, SwaggerUIStandalonePreset } from "swagger-ui-dist"
```

To get an absolute path to this directory for static file serving, use the exported `getAbsoluteFSPath` method:

```javascript
const swaggerUiAssetPath = require("swagger-ui-dist").getAbsoluteFSPath()

// then instantiate server that serves files from the swaggerUiAssetPath
```

For anything else, check the [Swagger-UI](https://github.com/swagger-api/swagger-ui) repository.
