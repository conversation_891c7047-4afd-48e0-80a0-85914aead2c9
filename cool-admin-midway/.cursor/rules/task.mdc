---
description: 任务与队列(Task)
globs: 
alwaysApply: false
---
# 任务与队列(Task)
# 任务与队列开发规则

## 基本规范

- 定时任务文件放在模块的`schedule`目录下
- 队列处理文件放在模块的`queue`目录下
- 文件命名使用下划线法，如：`data_sync.schedule.ts`、`email_sender.queue.ts`

## 任务类型

### 1. 定时任务

使用`@Schedule`装饰器配置定时任务：

```typescript
import { Provide } from '@midwayjs/core';
import { Inject } from '@midwayjs/core';
import { Schedule } from '@midwayjs/decorator';
import { XXXService } from '../service/xxx';

/**
 * 数据同步定时任务
 */
@Provide()
export class DataSyncSchedule {
  @Inject()
  xxxService: XXXService;
  
  /**
   * 每天凌晨1点执行
   */
  @Schedule({
    type: 'cron',
    cron: '0 0 1 * * *',
    // 是否立即启动
    immediate: false,
  })
  async exec() {
    console.log('数据同步定时任务开始执行');
    
    try {
      // 执行数据同步
      await this.xxxService.syncData();
    } catch (error) {
      console.error('数据同步定时任务执行失败', error);
    }
  }
}
```

### 2. 队列处理

使用`CoolQueue`装饰器配置队列：

```typescript
import { Provide } from '@midwayjs/core';
import { Inject } from '@midwayjs/core';
import { CoolQueue, Queue } from '@cool-midway/task';
import { XXXService } from '../service/xxx';

/**
 * 邮件发送队列
 */
@Provide()
@CoolQueue({
  type: 'bull', // 队列类型
  name: 'emailSender', // 队列名称
  concurrent: 5, // 并发数
  retry: 3, // 重试次数
})
export class EmailSenderQueue {
  @Inject()
  xxxService: XXXService;
  
  @Inject()
  queue: Queue;
  
  /**
   * 处理任务
   * @param job 任务数据
   */
  async process(job) {
    const { to, subject, content } = job.data;
    
    console.log('处理邮件发送任务', to, subject);
    
    try {
      // 发送邮件
      await this.xxxService.sendEmail(to, subject, content);
      return { success: true };
    } catch (error) {
      console.error('邮件发送失败', error);
      throw error; // 抛出异常会触发重试
    }
  }
  
  /**
   * 添加任务示例
   */
  async addTask() {
    // 添加邮件发送任务
    await this.queue.add('emailSender', {
      to: '<EMAIL>',
      subject: '测试邮件',
      content: '这是一封测试邮件'
    }, {
      delay: 1000, // 延迟1秒执行
      priority: 10, // 优先级
    });
  }
}
```

### 3. 延迟任务

使用队列实现延迟任务：

```typescript
import { Provide } from '@midwayjs/core';
import { Inject } from '@midwayjs/core';
import { Queue } from '@cool-midway/task';

/**
 * 延迟任务服务
 */
@Provide()
export class DelayTaskService {
  @Inject()
  queue: Queue;
  
  /**
   * 创建延迟任务
   * @param type 任务类型
   * @param data 任务数据
   * @param delay 延迟时间(毫秒)
   */
  async createDelayTask(type, data, delay = 0) {
    return this.queue.add(type, data, { delay });
  }
  
  /**
   * 创建定时任务
   * @param type 任务类型
   * @param data 任务数据
   * @param time 执行时间(Date对象)
   */
  async createScheduleTask(type, data, time) {
    const now = new Date();
    const delay = time.getTime() - now.getTime();
    
    if (delay <= 0) {
      throw new Error('执行时间必须大于当前时间');
    }
    
    return this.queue.add(type, data, { delay });
  }
}
```

## 最佳实践

- 定时任务应处理异常，避免因异常中断而无法继续执行
- 队列任务应具有幂等性，能够安全地重复执行
- 对于重要的任务，应实现日志记录和监控
- 队列任务应尽量小而专注，复杂任务可拆分为多个小任务
