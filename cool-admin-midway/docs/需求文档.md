# 考理论教育平台 - 需求文档

## 1. 项目概述

### 1.1 项目背景
考理论教育平台是一个面向各类职业资格考试和理论学习的在线教育平台，支持微信小程序、公众号和Web多端接入。平台提供题库练习、模拟考试、学习记录跟踪、在线支付和分销推广等功能，帮助用户高效备考和学习。

### 1.2 业务目标
1. 建立一个功能完善的在线教育平台，支持多种职业考试科目内容
2. 提供优质的用户学习体验，通过智能学习算法提高学习效率
3. 支持多端接入，实现用户数据统一管理和同步
4. 建立分销渠道，扩大用户覆盖面和市场占有率
5. 构建完整的内容管理和运营体系

### 1.3 用户群体
- **考生**: 备考各类职业资格证书的用户
- **从业人员**: 需要进行继续教育和理论学习的专业人士
- **教育机构**: 作为第三方内容提供商或分销渠道
- **平台管理员**: 负责内容管理、用户管理和系统维护的管理人员

## 2. 系统架构

### 2.1 总体架构
系统采用 Cool-Admin 全栈架构，分为前端层、应用层和数据层三层架构：

- **前端层**: 
  - 微信小程序端 (cool-admin-uni)
  - 微信公众号端 (cool-admin-uni)
  - H5/Web端 (cool-admin-uni)
  - 管理后台 (cool-admin-vue)

- **应用层**:
  - Node.js + Midway.js (cool-admin-midway 8.x)
  - 模块化设计，包括用户模块、内容模块、支付模块等

- **数据层**:
  - MySQL 数据库
  - Redis 缓存
  - 本地文件存储

### 2.2 技术选型
- **前端技术栈**: 
  - 管理端: Vue 3.x + Vite + TypeScript + Element Plus + Pinia
  - 用户端: uni-app + TypeScript + uView + Pinia

- **后端技术栈**:
  - cool-admin-midway 8.0.x
  - Node.js 18+
  - TypeORM 0.3.20
  - MySQL 8.0+
  - Redis 7.x

- **第三方集成**:
  - 微信小程序、公众号、支付 API
  - 阿里云存储/OSS
  - 短信服务

## 3. 功能需求

### 3.1 用户端功能

#### 3.1.1 用户认证与管理
- **微信授权登录**
  - 支持小程序一键登录
  - 支持公众号网页授权登录
  - UnionID 打通多平台账号体系
- **用户信息管理**
  - 个人资料管理：头像、昵称、手机号等
  - 地理位置服务：自动获取用户城市信息
  - 学习档案管理：学习进度、考试记录跟踪

#### 3.1.2 内容浏览与购买
- **科目分类浏览**
  - 按行业、地区分类展示科目
  - 科目详情查看
  - 本地化显示（根据用户城市显示对应名称）
- **多种支付方式**
  - 微信支付：小程序内原生支付
  - 支付宝支付：H5页面支付
  - 激活码兑换：支持批量分发和免费获取
- **权限管理**
  - 科目访问权限控制
  - 权限有效期管理
  - 到期提醒和续费机制

#### 3.1.3 学习功能
- **六种练习模式**
  - 顺序练习：按题目顺序系统学习
  - 题型练习：按题型分类练习
  - 错题练习：复习错题
  - 随机练习：随机抽取题目
  - 收藏练习：专项训练收藏题目
  - 搜题模式：关键词搜索题目
- **学习辅助功能**
  - 题目收藏与备注
  - 学习进度跟踪
  - 答题统计分析
  - 知识点掌握度评估

#### 3.1.4 考试功能
- **模拟考试**
  - 真实考试环境模拟
  - 严格时间控制
  - 成绩分析和错题解析
- **考试中断恢复机制**
  - 网络中断恢复
  - 应用切换限制
  - 异常退出重连

#### 3.1.5 分销推广功能
- **分销商管理**
  - 分销商申请与审核
  - 推广物料生成
  - 团队管理
- **佣金系统**
  - 佣金计算规则
  - 佣金结算与提现
  - 收益统计与分析

### 3.2 管理后台功能

#### 3.2.1 仪表盘(Dashboard)
- **工作台(Workspace)**
  - 关键业务指标卡片展示
  - 数据趋势图表
  - 实时数据监控
- **分析页(Analytics)**
  - 用户分析：增长、活跃度、留存率
  - 业务分析：订单、收入、科目热度
  - 地域分析：用户地域分布
- **监控页(Monitor)**
  - 系统状态监控
  - 业务监控
  - 告警中心

#### 3.2.2 内容管理
- **科目管理**
  - 科目CRUD操作
  - 价格方案设置
  - 本地化配置
- **题库管理**
  - 题目CRUD操作
  - 批量导入/导出
  - 题目质量审核
- **考试管理**
  - 试卷模板管理
  - 考试规则设置
  - 考试成绩统计

#### 3.2.3 用户管理
- **用户信息管理**
  - 用户列表与详情
  - 用户标签管理
  - 用户行为分析
- **权限管理**
  - 科目权限分配
  - 有效期管理
  - 批量操作

#### 3.2.4 订单与支付
- **订单管理**
  - 订单列表与详情
  - 状态管理
  - 异常订单处理
- **支付管理**
  - 支付记录管理
  - 对账功能
  - 异常处理
- **激活码管理**
  - 批量生成
  - 分发管理
  - 使用统计

#### 3.2.5 分销管理
- **分销商管理**
  - 申请审核
  - 资格管理
  - 业绩统计
- **佣金管理**
  - 佣金规则配置
  - 结算周期设置
  - 提现审核

#### 3.2.6 系统管理
- **管理员管理**
  - 角色权限管理
  - 操作日志
  - 登录安全设置
- **系统配置**
  - 基础参数设置
  - 支付接口配置
  - 微信接口配置

## 4. 业务规则

### 4.1 支付与订单规则
- **订单超时与状态管理**
  - 订单号格式：KL + 年月日 + 6位随机数
  - 未支付订单30分钟后自动取消
  - 支付异常处理机制
  - 订单不支持退款政策
- **科目权限管理**
  - 支付成功后立即生效
  - 激活码兑换后立即生效
  - 到期前7天、1天发送提醒
  - 权限续费规则

### 4.2 学习与考试规则
- **学习进度管理**
  - 完成章节内80%题目算完成该章节
  - 整体进度按加权平均计算
- **考试规则**
  - 考试资格验证
  - 考试中断恢复机制
  - 成绩计算规则

### 4.3 分销业务规则
- **分销商资格管理**
  - 申请条件：注册满7天，至少购买1个科目，无违规记录
  - 48小时内完成审核
- **佣金计算规则**
  - 一级分销商15%，二级分销商5%
  - 订单支付成功后7天可申请提现

### 4.4 激活码规则
- **激活码生成规则**
  - 8位字符，包含数字和大写字母，排除易混淆字符
  - 状态定义：未使用、已使用、已过期、已禁用、异常
- **防重复使用机制**
  - 使用验证流程
  - 并发使用防护
  - 异常使用检测

### 4.5 智能学习算法
- **题目优先级算法**
  - 未答题目最高优先级
  - 答错题目优先级次之
  - 考虑收藏状态和时间衰减因素
- **本地化显示机制**
  - 支持模板语法：{city}{name}等
  - 根据用户位置提供本地化内容

## 5. 非功能需求

### 5.1 性能需求
- 页面响应时间：小于2秒
- 并发用户数：支持1000+同时在线用户
- 数据库查询响应：小于1秒
- 移动端加载优化：首屏渲染时间小于3秒

### 5.2 安全需求
- 用户数据加密存储
- 防SQL注入、XSS攻击
- HTTPS安全传输
- 敏感操作日志记录
- 权限细粒度控制

### 5.3 可用性需求
- 系统稳定性：99.9%可用率
- 数据备份：每日自动备份
- 故障恢复：支持快速故障恢复
- 多终端适配：支持各类移动设备

### 5.4 可扩展性需求
- 支持水平扩展：可通过增加服务器节点提高系统容量
- 模块化设计：方便功能扩展
- 第三方集成能力：预留API接口

## 6. 系统集成

### 6.1 微信生态集成
- **微信小程序**
  - 登录认证
  - 支付功能
  - 消息推送
- **微信公众号**
  - 网页授权
  - 自定义菜单
  - 消息处理
  - 模板消息
- **微信支付**
  - 统一下单
  - 支付回调
  - 退款处理

### 6.2 其他第三方集成
- **支付宝支付**
  - H5支付
  - 支付回调
- **短信服务**
  - 验证码
  - 通知短信
- **对象存储**
  - 图片、文件存储
  - CDN加速

## 7. 部署与运维

### 7.1 部署环境
- **开发环境**：用于开发和测试
- **测试环境**：用于QA测试和验收
- **生产环境**：正式运行环境

### 7.2 服务器配置
- **应用服务器**：2核4G以上，存储60GB以上
- **数据库服务器**：4核8G以上，存储100GB以上
- **缓存服务器**：2核4G以上

### 7.3 运维要求
- 监控告警系统
- 定期备份机制
- 日志收集与分析
- 性能监控与优化

## 8. 项目风险与应对策略

### 8.1 技术风险
- **风险**：新技术栈可能存在不稳定因素
- **应对**：充分测试，准备备选方案

### 8.2 业务风险
- **风险**：用户接受度不确定
- **应对**：先小范围测试，收集反馈后迭代

### 8.3 数据安全风险
- **风险**：用户数据泄露
- **应对**：实施严格的数据安全策略，定期安全审计

## 9. 实施计划

### 9.1 开发阶段
1. **需求分析与设计**：确定需求范围，完成系统设计
2. **核心功能开发**：用户认证、内容管理、学习功能
3. **扩展功能开发**：考试系统、支付系统、分销系统
4. **系统集成**：微信接入、支付集成、第三方服务

### 9.2 测试阶段
1. **单元测试**：各功能模块测试
2. **集成测试**：系统集成后的功能测试
3. **性能测试**：系统负载和性能测试
4. **用户测试**：邀请部分用户进行体验反馈

### 9.3 上线与运维
1. **环境部署**：生产环境搭建与配置
2. **数据迁移**：初始数据准备与导入
3. **上线计划**：分批次逐步上线
4. **运维支持**：系统监控与维护

## 10. 附录

### 10.1 术语表
- **Cool-Admin**：一套基于 Node.js 和 Vue 的前后端分离开发框架
- **UnionID**：微信提供的用于多平台账号打通的唯一标识
- **RBAC**：基于角色的访问控制，一种权限管理模式

### 10.2 相关文档
- 数据模型文档
- 用户流程文档
- 技术架构文档
- 业务规则文档
- 微信接入文档 