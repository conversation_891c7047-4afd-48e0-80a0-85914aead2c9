# 考理论教育平台 - 业务规则文档

## 支付与订单业务规则

### 订单超时和状态管理
1. **订单创建规则**：
   - 订单号格式：KL + 年月日 + 6位随机数（如：KL20241201123456）
   - 订单状态：0-待支付、1-已支付、2-已取消、3-异常

2. **订单超时处理**：
   - **超时时间**：未支付订单30分钟后自动取消
   - **超时检查**：每5分钟扫描一次超时订单
   - **自动取消流程**：订单状态改为"已取消" → 释放库存 → 发送取消通知

3. **支付异常处理**：
   - **系统异常**：支付成功但权限开通失败，人工处理后开通权限
   - **重复支付**：同一用户同一科目15分钟内只能有一个有效订单
   - **金额异常**：支付金额与订单金额不符，标记异常订单人工处理
   - **不支持退款**：所有订单一经支付成功不支持退款，请用户购买前仔细确认

### 科目权限管理
1. **权限生效规则**：
   - 支付成功后立即生效
   - 激活码兑换后立即生效
   - 权限到期前7天、1天发送提醒

2. **权限续费规则**：
   - 到期前可续费，时间累加
   - 到期后30天内可续费，重新计算时间
   - 超过30天需重新购买

## 学习与考试业务规则

### 学习进度管理
1. **进度计算规则**：
   - 章节练习：完成章节内80%题目算完成该章节
   - 整体进度：所有章节完成度的加权平均
   - 学习时长：实际答题时间+查看解析时间

### 考试规则与中断恢复机制
1. **考试资格验证**：
   - 必须拥有科目有效权限（未过期）
   - 建议完成练习进度>60%（可强制参加，但会提示风险）
   - 同一考试每天最多参加3次，两次考试间隔至少10分钟

2. **考试中断恢复机制**：
   - **数据保存策略**：每答完一题自动保存到本地缓存，每5题同步一次到服务器
   - **网络中断处理**：本地缓存继续答题，网络恢复后自动同步
   - **应用切换处理**：第3次切换自动提交考试，单次切换超过2分钟自动提交
   - **异常退出恢复**：重新进入时检测未完成考试，扣除中断时间（最多10分钟）后继续

3. **成绩计算规则**：
   - **题型分值**：单选题1分/题，多选题2分/题（全对得2分，少选得1分），判断题1分/题
   - **特殊情况处理**：未答题目按错误计算，系统异常导致的未答题不计入总分

## 分销业务规则

### 分销商资格管理
1. **申请条件**：注册用户满7天，至少购买过1个科目，无违规记录
2. **审核标准**：身份信息真实有效，推广渠道合规，48小时内完成审核

### 佣金计算规则
1. **佣金比例**：一级分销商15%，二级分销商5%，特殊科目可设置不同比例
2. **佣金结算**：订单支付成功后7天可申请提现，异常订单人工审核后结算

## 激活码管理与防重复使用机制

### 激活码生成与管理
1. **激活码生成规则**：
   - **格式规范**：8位字符，包含数字和大写字母，排除易混淆字符（0、O、1、I）
   - **状态定义**：0-未使用、1-已使用、2-已过期、3-已禁用、4-异常

### 激活码防重复使用机制
1. **使用验证流程**：
   ```
   格式验证 → 存在性验证 → 状态验证 → 权限验证 → 使用记录
   ```

2. **并发使用防护**：
   - 数据库锁定：使用行级锁锁定激活码记录
   - 原子操作：状态检查和更新在同一事务中完成
   - 重复检测：更新前再次检查状态

3. **异常使用检测**：
   - 同一用户短时间内使用多个激活码
   - 同一设备多个账号使用激活码
   - 激活码使用地域异常（如海外IP）

## 微信接入业务规则

### 基础规则
- **UnionID统一身份**：使用微信UnionID作为用户唯一标识，实现跨平台账号打通
- **数据同步**：小程序和公众号学习数据实时同步
- **消息推送**：学习提醒、考试通知、权限到期提醒等，详见[微信接入文档](./微信接入文档.md)

## 智能学习算法规则

### 题目优先级算法
**未答题目优先策略**：
```
题目优先级计算规则：
1. 未答题目：优先级 = 100（最高优先级）
2. 答错题目：优先级 = 80 - (错误次数 * 5)
3. 答对题目：优先级 = 60 - (正确次数 * 2)
4. 收藏题目：优先级 + 20（加分项）
5. 最近答题：优先级 - (天数差 * 2)（时间衰减）

最终显示顺序：按优先级从高到低排序
```

### 本地化显示机制
**科目显示模板**：
- **模板语法**：`{city}{name}` - 城市+科目名称
- **变量支持**：
  - `{city}` - 用户所在城市
  - `{name}` - 科目名称
  - `{month}` - 当前月份
  - `{category}` - 分类名称

**示例效果**：
- 模板：`{city}{name}`
- 科目：特种设备考试
- 用户在合肥看到：合肥特种设备考试
- 用户在芜湖看到：芜湖特种设备考试

## 数据安全与隐私保护

### 用户数据保护
1. **数据收集原则**：最小化收集，明确告知用途
2. **数据存储安全**：加密存储敏感信息
3. **数据访问控制**：严格的权限管理
4. **数据删除机制**：用户注销后数据处理

### 支付数据安全
1. **支付信息加密**：所有支付相关数据加密传输和存储
2. **交易记录保护**：支付记录访问权限严格控制
3. **异常监控**：实时监控异常支付行为
4. **合规要求**：符合支付行业数据安全标准

### 学习数据隐私
1. **学习记录保护**：个人学习数据仅用于个性化服务
2. **成绩隐私**：考试成绩仅用户本人可见
3. **行为分析**：用户行为数据仅用于产品优化
4. **数据共享**：不向第三方分享用户个人数据
