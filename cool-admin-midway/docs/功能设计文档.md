# 考理论教育平台 - 功能设计文档

## 用户端功能

### 用户认证与管理
- **微信授权登录**：一键登录，获取用户基本信息
- **多平台账号打通**：小程序、公众号、H5统一账号体系
- **个人信息管理**：头像、昵称、手机号等信息维护
- **位置服务**：自动获取用户城市，提供本地化服务
- **学习档案**：学习进度、考试记录、成就展示

### 内容浏览与购买
- **科目分类浏览**：按行业、地区分类展示科目
- **科目详情查看**：科目介绍、题目数量、价格信息
- **本地化显示**：根据用户城市显示对应的科目名称
- **多种支付方式**：
  - 微信支付：小程序内原生支付体验
  - 支付宝支付：H5页面支付，覆盖更多用户
  - 激活码兑换：支持批量分发和免费获取
- **购买须知**：明确提示不支持退款政策

### 学习功能
**六种练习模式**（均优先显示未答题目）：
- **顺序练习**：按题目顺序系统学习
- **题型练习**：按题型分类练习（单选、多选、判断）
- **错题练习**：针对性复习错题，重点突破薄弱环节
- **随机练习**：随机抽取题目练习，增加学习趣味性
- **收藏练习**：重点题目专项训练
- **搜题模式**：关键词搜索题目，快速定位特定内容

**学习辅助功能**：
- 题目收藏与个人备注
- 学习进度实时跟踪
- 答题统计分析
- 知识点掌握度评估
- 学习时长统计

### 考试功能
- **模拟考试**：真实考试环境模拟，严格时间控制
- **考试中断恢复**：网络中断、应用切换等异常情况的恢复机制
- **防作弊机制**：切换应用次数限制、时间监控
- **成绩分析**：详细的成绩报告和错题分析
- **历史记录**：考试历史和成绩趋势分析

### 社交与激励
- **排行榜系统**：学习时长、考试成绩等多维度排名
- **成就系统**：学习里程碑和徽章奖励
- **分享功能**：学习成果和考试成绩分享到微信

### 分销推广功能
- **分销商申请**：用户申请成为分销商，获得推广资格
- **推广物料**：专属推广链接、二维码、宣传海报
- **佣金管理**：佣金统计、提现申请、收益明细
- **团队管理**：下级分销商管理、团队业绩统计

## 管理后台功能 (Vue Shop Vite)

### 仪表盘 (Dashboard)
基于 **Element Plus** 和 **ECharts** 构建的数据可视化仪表盘：

#### 工作台 (Workspace)
- **核心指标卡片**：使用 `el-card` 展示关键业务指标
  - 今日新增用户、活跃用户、订单数量、收入金额
  - 同比环比增长率，趋势图表展示
- **数据图表**：集成 ECharts 图表库
  - 用户增长趋势图、收入趋势图
  - 科目销售排行榜、地区分布图
  - 学习时长分布、考试通过率统计
- **实时数据**：WebSocket 实时更新关键指标

#### 分析页 (Analytics)
- **用户分析**：用户增长、活跃度、留存率分析
- **业务分析**：订单转化、收入分析、科目热度
- **地域分析**：用户地域分布、地区收入对比
- **时间分析**：按时间维度的各项指标趋势

#### 监控页 (Monitor)
- **系统状态监控**：服务器性能、数据库连接、Redis状态
- **业务监控**：API响应时间、错误率、并发用户数
- **告警中心**：异常事件实时告警和处理
- **日志查看**：系统日志、错误日志、操作日志

### 内容管理
使用 **Element Plus** 组件构建的内容管理系统：

#### 科目管理
- **科目列表**：`el-table` 数据表格，支持搜索、筛选、排序
  - 科目名称、分类、价格、状态、创建时间
  - 批量操作：上下架、删除、导出
  - 高级搜索：按分类、价格区间、状态筛选
- **科目编辑**：`el-form` 表单，支持分步骤填写
  - 基本信息：名称、描述、分类、价格
  - 本地化配置：显示模板、地区设置
  - 权限设置：有效期、限制条件
  - 封面图片：支持图片上传和裁剪
- **分类管理**：`el-tree` 树形控件，支持拖拽排序
  - 分类层级管理，无限级分类
  - 分类图标设置，分类描述

#### 题库管理
- **题目列表**：`el-table` 表格展示，支持题型筛选
  - 题目内容、题型、难度、所属科目
  - 批量操作：导入、导出、删除、审核
- **题目编辑器**：富文本编辑器，支持图片、公式
  - 题目内容编辑，支持Markdown
  - 选项设置，答案配置
  - 解析说明，知识点标签
- **批量导入**：Excel/CSV 文件批量导入
  - 模板下载，格式验证
  - 导入预览，错误提示
- **质量审核**：题目审核流程，状态管理
  - 审核队列，审核记录
  - 审核意见，修改建议

#### 考试管理
- **试卷模板**：可视化试卷构建器
  - 拖拽式题目组合
  - 题型比例设置
  - 难度分布配置
- **考试规则**：时间限制、题目数量、及格分数
  - 考试时长设置
  - 题目随机抽取规则
  - 成绩计算方式
- **考试发布**：定时发布、权限控制
  - 发布时间设置
  - 参与用户范围
  - 考试结果统计

### 用户管理
#### 用户信息管理
- **用户列表**：`ProTable` 展示用户信息
  - 头像、昵称、手机号、注册时间、最后登录
  - 高级搜索：按注册时间、活跃度、地区筛选
- **用户详情**：`ProDescriptions` 详情展示
  - 基本信息、学习数据、购买记录、分销信息
- **用户标签**：自定义标签管理，用户分组

#### 权限管理
- **科目权限**：用户科目权限分配和管理
- **有效期管理**：权限到期提醒和续费
- **批量操作**：批量分配权限、延期等

#### 学习数据分析
- **学习进度**：可视化学习进度展示
- **考试记录**：详细考试历史和成绩分析
- **行为分析**：学习习惯、活跃时段分析

### 订单与支付
#### 订单管理
- **订单列表**：`ProTable` 订单管理
  - 订单号、用户、商品、金额、状态、支付方式
  - 状态筛选：待支付、已支付、已取消、已退款
- **订单详情**：完整订单信息展示
- **异常处理**：订单异常标记和处理流程

#### 支付管理
- **支付记录**：支付流水详细记录
- **对账管理**：与第三方支付平台对账
- **异常订单**：支付异常订单处理

#### 激活码管理
- **批量生成**：`ProForm` 表单配置生成规则
- **分发管理**：激活码分发记录和统计
- **使用统计**：激活码使用情况分析

### 分销管理
#### 分销商管理
- **申请审核**：分销商申请审核流程
- **资格管理**：分销商等级和权限管理
- **业绩统计**：分销商业绩排行和分析

#### 佣金管理
- **佣金规则**：灵活的佣金规则配置
- **结算管理**：佣金结算周期和规则
- **提现审核**：提现申请审核流程

#### 推广数据
- **推广效果**：推广链接点击和转化统计
- **转化分析**：推广渠道效果分析

### 微信管理
#### 公众号管理
- **菜单配置**：可视化菜单编辑器
- **自动回复**：关键词回复规则设置
- **素材管理**：图片、视频等素材库

#### 消息推送
- **模板消息**：消息模板管理和发送
- **群发消息**：用户群发消息功能
- **定时推送**：定时消息推送设置

### 数据分析
#### 运营数据
- **用户增长**：新增、活跃、留存用户分析
- **渠道分析**：不同渠道用户质量对比
- **漏斗分析**：用户转化路径分析

#### 学习数据
- **学习统计**：学习时长、完成率统计
- **知识点分析**：知识点掌握度热力图
- **学习路径**：用户学习路径分析

#### 财务数据
- **收入统计**：收入趋势和构成分析
- **成本分析**：运营成本和ROI分析
- **财务报表**：自动生成财务报表

### 系统管理
#### 管理员管理
- **角色权限**：基于RBAC的权限管理
- **操作日志**：管理员操作记录
- **登录安全**：登录日志和安全设置

#### 系统配置
- **基础配置**：系统基本参数设置
- **支付配置**：支付接口参数配置
- **微信配置**：微信接口参数设置

## 核心特性

### 智能学习算法
- **题目优先级算法**：未答题目优先显示，详见[业务规则文档](./业务规则文档.md#智能学习算法规则)
- **个性化推荐**：基于学习行为的智能推荐
- **学习路径规划**：自适应学习路径

### 本地化显示
- **科目显示模板**：支持`{city}{name}`等变量，详见[业务规则文档](./业务规则文档.md#本地化显示机制)
- **地域化服务**：根据用户位置提供本地化内容
- **多语言支持**：预留国际化扩展能力
