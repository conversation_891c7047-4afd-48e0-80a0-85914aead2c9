# 考理论教育平台 - 微信接入文档

## 微信生态架构

### 整体接入方案
```
┌─────────────────────────────────────────────────────────┐
│                    微信生态接入                          │
├─────────────────────────────────────────────────────────┤
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐  │
│  │  微信小程序  │    │  微信公众号  │    │   微信支付   │  │
│  │             │    │             │    │             │  │
│  │ • 学习练习   │    │ • 内容推送   │    │ • 订单支付   │  │
│  │ • 模拟考试   │    │ • 菜单导航   │    │ • 退款处理   │  │
│  │ • 用户中心   │    │ • 客服消息   │    │ • 对账管理   │  │
│  │ • 分销推广   │    │ • H5页面    │    │             │  │
│  └─────────────┘    └─────────────┘    └─────────────┘  │
│           │                 │                 │         │
│           └─────────────────┼─────────────────┘         │
│                             │                           │
│  ┌─────────────────────────────────────────────────────┐  │
│  │              UnionID 统一身份认证                    │  │
│  │  • 小程序和公众号账号打通                            │  │
│  │  • 跨平台数据同步                                   │  │
│  │  • 统一用户体验                                     │  │
│  └─────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────┘
```

## 微信小程序接入

### 小程序配置
```javascript
// app.json
{
  "appid": "wx1234567890abcdef",
  "pages": [
    "pages/index/index",
    "pages/subjects/subjects",
    "pages/study/study",
    "pages/exam/exam",
    "pages/profile/profile"
  ],
  "permission": {
    "scope.userLocation": {
      "desc": "获取位置信息用于本地化服务"
    }
  },
  "requiredBackgroundModes": ["audio"],
  "networkTimeout": {
    "request": 10000,
    "downloadFile": 10000
  }
}
```

### 用户授权登录
```javascript
// 小程序登录流程
const loginWithWechat = async () => {
  try {
    // 1. 获取登录凭证
    const { code } = await wx.login();

    // 2. 获取用户信息授权
    const { userInfo } = await wx.getUserProfile({
      desc: '用于完善用户资料'
    });

    // 3. 发送到后端验证
    const response = await request('/api/auth/wechat/login', {
      method: 'POST',
      data: {
        code,
        userInfo,
        platform: 'miniprogram'
      }
    });

    // 4. 保存用户信息和token
    wx.setStorageSync('token', response.token);
    wx.setStorageSync('userInfo', response.user);

    return response;
  } catch (error) {
    console.error('登录失败:', error);
    throw error;
  }
};
```

### 支付功能
```javascript
// 微信支付
const wechatPay = async (orderId) => {
  try {
    // 1. 获取支付参数
    const payParams = await request('/api/payment/wechat/prepare', {
      method: 'POST',
      data: { orderId }
    });

    // 2. 调起支付
    await wx.requestPayment({
      timeStamp: payParams.timeStamp,
      nonceStr: payParams.nonceStr,
      package: payParams.package,
      signType: payParams.signType,
      paySign: payParams.paySign
    });

    // 3. 支付成功处理
    wx.showToast({
      title: '支付成功',
      icon: 'success'
    });

    // 4. 跳转到学习页面
    wx.navigateTo({
      url: '/pages/study/study?subjectId=' + subjectId
    });

  } catch (error) {
    if (error.errMsg === 'requestPayment:fail cancel') {
      wx.showToast({
        title: '支付已取消',
        icon: 'none'
      });
    } else {
      wx.showToast({
        title: '支付失败',
        icon: 'error'
      });
    }
  }
};
```

## 微信公众号接入

### 公众号配置
```javascript
// 公众号基础配置
const wechatConfig = {
  appId: 'wx0987654321fedcba',
  appSecret: 'your_official_account_secret',
  token: 'your_token',
  encodingAESKey: 'your_aes_key',

  // 服务器配置
  serverUrl: 'https://your-domain.com/api/wechat/callback',

  // 网页授权域名
  oauthDomain: 'your-domain.com',

  // JS接口安全域名
  jsDomain: 'your-domain.com'
};
```

### 消息处理服务
```javascript
// 微信消息处理
const handleWechatMessage = async (req, res) => {
  const { signature, timestamp, nonce, echostr } = req.query;
  const { xml } = req.body;

  // 1. 验证签名
  if (!verifySignature(signature, timestamp, nonce)) {
    return res.status(401).send('Unauthorized');
  }

  // 2. 首次验证
  if (echostr) {
    return res.send(echostr);
  }

  // 3. 解析消息
  const message = await parseXML(xml);

  // 4. 处理不同类型消息
  let response;
  switch (message.MsgType) {
    case 'text':
      response = await handleTextMessage(message);
      break;
    case 'event':
      response = await handleEventMessage(message);
      break;
    case 'image':
      response = await handleImageMessage(message);
      break;
    default:
      response = await getDefaultResponse(message);
  }

  // 5. 返回响应
  res.set('Content-Type', 'application/xml');
  res.send(buildXMLResponse(response));
};

// 文本消息处理
const handleTextMessage = async (message) => {
  const { FromUserName, Content } = message;

  // 关键词回复
  const keywords = {
    '学习': '点击菜单"学习中心"开始学习',
    '考试': '点击菜单"模拟考试"进行测试',
    '客服': '请拨打客服电话：400-123-4567',
    '帮助': '使用帮助：\n1. 选择科目\n2. 开始练习\n3. 模拟考试'
  };

  const reply = keywords[Content] || '感谢您的消息，请使用菜单功能或回复"帮助"查看使用指南';

  return {
    ToUserName: FromUserName,
    FromUserName: message.ToUserName,
    CreateTime: Date.now(),
    MsgType: 'text',
    Content: reply
  };
};

// 事件消息处理
const handleEventMessage = async (message) => {
  const { FromUserName, Event, EventKey } = message;

  switch (Event) {
    case 'subscribe':
      // 关注事件
      await handleSubscribe(FromUserName);
      return getWelcomeMessage(FromUserName, message.ToUserName);

    case 'unsubscribe':
      // 取消关注事件
      await handleUnsubscribe(FromUserName);
      break;

    case 'CLICK':
      // 菜单点击事件
      return await handleMenuClick(FromUserName, EventKey);

    case 'VIEW':
      // 菜单跳转事件
      await logMenuView(FromUserName, EventKey);
      break;
  }

  return null;
};
```

### 自定义菜单配置
```javascript
// 创建自定义菜单
const createMenu = async () => {
  const menuData = {
    button: [
      {
        name: "学习中心",
        sub_button: [
          {
            type: "miniprogram",
            name: "开始学习",
            appid: "wx1234567890abcdef",
            pagepath: "pages/subjects/subjects"
          },
          {
            type: "miniprogram",
            name: "模拟考试",
            appid: "wx1234567890abcdef",
            pagepath: "pages/exam/exam"
          },
          {
            type: "view",
            name: "学习进度",
            url: "https://your-domain.com/h5/progress"
          }
        ]
      },
      {
        name: "我的课程",
        sub_button: [
          {
            type: "miniprogram",
            name: "已购课程",
            appid: "wx1234567890abcdef",
            pagepath: "pages/profile/courses"
          },
          {
            type: "view",
            name: "考试记录",
            url: "https://your-domain.com/h5/records"
          },
          {
            type: "view",
            name: "成绩查询",
            url: "https://your-domain.com/h5/scores"
          }
        ]
      },
      {
        name: "客服帮助",
        sub_button: [
          {
            type: "click",
            name: "联系客服",
            key: "CONTACT_SERVICE"
          },
          {
            type: "view",
            name: "使用帮助",
            url: "https://your-domain.com/h5/help"
          },
          {
            type: "click",
            name: "意见反馈",
            key: "FEEDBACK"
          }
        ]
      }
    ]
  };

  try {
    const accessToken = await getAccessToken();
    const response = await axios.post(
      `https://api.weixin.qq.com/cgi-bin/menu/create?access_token=${accessToken}`,
      menuData
    );

    if (response.data.errcode === 0) {
      console.log('菜单创建成功');
    } else {
      console.error('菜单创建失败:', response.data);
    }
  } catch (error) {
    console.error('菜单创建异常:', error);
  }
};
```

### 模板消息推送
```javascript
// 发送模板消息
const sendTemplateMessage = async (openId, templateId, data, miniprogram = null) => {
  try {
    const accessToken = await getAccessToken();

    const messageData = {
      touser: openId,
      template_id: templateId,
      data: data,
      miniprogram: miniprogram
    };

    const response = await axios.post(
      `https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=${accessToken}`,
      messageData
    );

    return response.data;
  } catch (error) {
    console.error('模板消息发送失败:', error);
    throw error;
  }
};

// 学习提醒消息
const sendStudyReminder = async (user) => {
  const templateId = 'your_study_reminder_template_id';

  const data = {
    first: {
      value: '学习提醒',
      color: '#173177'
    },
    keyword1: {
      value: user.nickname,
      color: '#173177'
    },
    keyword2: {
      value: '您已经3天没有学习了，继续加油！',
      color: '#173177'
    },
    remark: {
      value: '点击继续学习，保持学习习惯',
      color: '#173177'
    }
  };

  const miniprogram = {
    appid: 'wx1234567890abcdef',
    pagepath: 'pages/study/study'
  };

  return await sendTemplateMessage(user.openId, templateId, data, miniprogram);
};

// 考试成绩通知
const sendExamResult = async (user, examResult) => {
  const templateId = 'your_exam_result_template_id';

  const data = {
    first: {
      value: '考试成绩通知',
      color: '#173177'
    },
    keyword1: {
      value: examResult.subjectName,
      color: '#173177'
    },
    keyword2: {
      value: `${examResult.score}分`,
      color: examResult.isPassed ? '#00AA00' : '#FF0000'
    },
    keyword3: {
      value: examResult.isPassed ? '通过' : '未通过',
      color: examResult.isPassed ? '#00AA00' : '#FF0000'
    },
    remark: {
      value: examResult.isPassed ? '恭喜通过考试！' : '继续努力，再接再厉！',
      color: '#173177'
    }
  };

  return await sendTemplateMessage(user.openId, templateId, data);
};
```

## 统一身份认证

### UnionID机制
```javascript
// 用户身份统一处理
const unifyUserIdentity = async (wechatData) => {
  const { openId, unionId, platform, userInfo } = wechatData;

  try {
    // 1. 查找现有用户
    let user = null;
    if (unionId) {
      // 优先通过UnionID查找
      user = await User.findOne({
        include: [{
          model: WechatAccount,
          where: { unionId }
        }]
      });
    }

    if (!user) {
      // 通过OpenID查找
      user = await User.findOne({
        include: [{
          model: WechatAccount,
          where: { openId }
        }]
      });
    }

    // 2. 创建或更新用户
    if (!user) {
      // 创建新用户
      user = await User.create({
        nickname: userInfo.nickName,
        avatar: userInfo.avatarUrl,
        city: userInfo.city,
        province: userInfo.province
      });
    } else {
      // 更新用户信息
      await user.update({
        nickname: userInfo.nickName || user.nickname,
        avatar: userInfo.avatarUrl || user.avatar,
        city: userInfo.city || user.city,
        province: userInfo.province || user.province
      });
    }

    // 3. 创建或更新微信账号记录
    const [wechatAccount] = await WechatAccount.findOrCreate({
      where: { openId },
      defaults: {
        userId: user.id,
        unionId,
        openId,
        platform,
        nickname: userInfo.nickName,
        avatar: userInfo.avatarUrl
      }
    });

    // 4. 更新微信账号信息
    if (wechatAccount) {
      await wechatAccount.update({
        unionId: unionId || wechatAccount.unionId,
        nickname: userInfo.nickName || wechatAccount.nickname,
        avatar: userInfo.avatarUrl || wechatAccount.avatar
      });
    }

    return { user, wechatAccount };
  } catch (error) {
    console.error('用户身份统一失败:', error);
    throw error;
  }
};
```

### 跨平台数据同步
```javascript
// 数据同步服务
const syncUserData = async (userId) => {
  try {
    // 获取用户的所有微信账号
    const wechatAccounts = await WechatAccount.findAll({
      where: { userId }
    });

    // 同步学习进度
    const studyProgress = await StudyRecord.findAll({
      where: { userId },
      include: [Subject, Question]
    });

    // 同步考试记录
    const examRecords = await ExamRecord.findAll({
      where: { userId },
      include: [Subject]
    });

    // 同步错题记录
    const wrongQuestions = await WrongQuestion.findAll({
      where: { userId },
      include: [Question]
    });

    // 同步收藏题目
    const favoriteQuestions = await FavoriteQuestion.findAll({
      where: { userId },
      include: [Question]
    });

    return {
      wechatAccounts,
      studyProgress,
      examRecords,
      wrongQuestions,
      favoriteQuestions
    };
  } catch (error) {
    console.error('数据同步失败:', error);
    throw error;
  }
};
```

## 数据模型

微信相关的数据模型详见[数据模型文档](./数据模型文档.md#微信体系模型)，包括：
- **WechatMessage**：微信消息记录
- **WechatMenu**：微信菜单配置
- **WechatAccount**：微信账号信息

## 接入流程

### 开发环境配置
1. **申请微信开发者账号**
2. **创建小程序和公众号**
3. **配置服务器域名**
4. **设置支付商户号**
5. **配置回调地址**

### 生产环境部署
1. **域名备案和SSL证书**
2. **微信认证和审核**
3. **支付功能开通**
4. **菜单和模板配置**
5. **功能测试和上线**
