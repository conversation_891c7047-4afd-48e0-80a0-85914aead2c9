import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '../../base/entity/base';

/**
 * 题目信息
 */
@Entity('question')
export class QuestionEntity extends BaseEntity {
  @Index()
  @Column({ comment: '所属科目ID', type: 'int', nullable: true })
  subjectId: number;

  @Index()
  @Column({ comment: '省份/直辖市ID', type: 'int' })
  provinceId: number;

  @Index()
  @Column({ comment: '分类ID', type: 'int', nullable: true })
  categoryId: number;

  @Column({
    comment: '题目类型',
    type: 'tinyint',
    default: 0,
    dict: ['单选', '多选', '判断']
  })
  type: number;

  @Column({ comment: '题目内容', type: 'text' })
  content: string;

  @Column({ comment: '题目图片', nullable: true, length: 500 })
  image: string;

  @Column({ comment: '题目解析', type: 'text', nullable: true })
  analysis: string;

  @Column({
    comment: '难度级别',
    type: 'tinyint',
    default: 0,
    dict: ['简单', '中等', '困难']
  })
  difficulty: number;

  @Column({ comment: '题目分数', type: 'decimal', precision: 5, scale: 2, default: 1.0 })
  score: number;

  @Column({ comment: '排序号', type: 'int', default: 0 })
  orderNum: number;

  @Column({
    comment: '状态',
    type: 'tinyint',
    default: 1,
    dict: ['禁用', '启用']
  })
  status: number;
}
