import { Provide } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository, In } from 'typeorm';
import { QuestionEntity } from '../entity/question';
import { QuestionOptionEntity } from '../entity/option';
import { CategoryEntity } from '../../category/entity/category';
import { SubjectEntity } from '../../subject/entity/subject';

/**
 * 题目服务
 */
@Provide()
export class QuestionService extends BaseService {
  @InjectEntityModel(QuestionEntity)
  questionEntity: Repository<QuestionEntity>;

  @InjectEntityModel(QuestionOptionEntity)
  questionOptionEntity: Repository<QuestionOptionEntity>;

  @InjectEntityModel(CategoryEntity)
  categoryEntity: Repository<CategoryEntity>;

  @InjectEntityModel(SubjectEntity)
  subjectEntity: Repository<SubjectEntity>;

  /**
   * 新增题目
   */
  async add(param: any) {
    // 直接保存题目（选项已经是JSON字符串）
    const question = await this.questionEntity.save(param);
    return question;
  }

  /**
   * 更新题目
   */
  async update(param: any) {
    const { id, options, ...questionData } = param;

    // 更新题目
    await this.questionEntity.update(id, questionData);

    // 删除原有选项
    await this.questionOptionEntity.delete({ questionId: id });

    // 保存新选项
    if (options && options.length > 0) {
      const optionEntities = options.map((option: any, index: number) => ({
        ...option,
        questionId: id,
        orderNum: option.orderNum || index + 1,
      }));
      await this.questionOptionEntity.save(optionEntities);
    }
  }

  /**
   * 更新题目（包含选项）并返回详情
   */
  async updateWithOptions(param: any) {
    await this.update(param);
    return await this.info({ id: param.id });
  }

  /**
   * 删除题目
   */
  async delete(ids: any) {
    const questionIds = Array.isArray(ids) ? ids : [ids];

    // 删除选项
    await this.questionOptionEntity
      .createQueryBuilder()
      .delete()
      .where('questionId IN (:...ids)', { ids: questionIds })
      .execute();

    // 删除题目
    await this.questionEntity.delete(questionIds);
  }

  /**
   * 获取题目详情（包含选项）
   */
  async info(param: any) {
    const { id } = param;

    // 获取题目信息
    const question = await this.questionEntity.findOne({ where: { id } });
    if (!question) {
      return null;
    }

    // 获取选项
    const options = await this.questionOptionEntity.find({
      where: { questionId: id },
      order: { orderNum: 'ASC' },
    });

    return {
      ...question,
      options,
    };
  }

  /**
   * 分页查询题目（包含选项）
   */
  async page(query: any) {
    const { provinceId, categoryId, subjectId, type, difficulty, status, keyWord } = query;

    const queryBuilder = this.questionEntity.createQueryBuilder('question');

    // 按省份筛选
    if (provinceId) {
      queryBuilder.andWhere('question.provinceId = :provinceId', { provinceId });
    }

    // 按分类筛选
    if (categoryId) {
      queryBuilder.andWhere('question.categoryId = :categoryId', { categoryId });
    }

    // 按科目筛选
    if (subjectId) {
      queryBuilder.andWhere('question.subjectId = :subjectId', { subjectId });
    }

    // 按题目类型筛选
    if (type !== undefined && type !== null) {
      queryBuilder.andWhere('question.type = :type', { type });
    }

    // 按难度筛选
    if (difficulty !== undefined && difficulty !== null) {
      queryBuilder.andWhere('question.difficulty = :difficulty', { difficulty });
    }

    // 按状态筛选
    if (status !== undefined && status !== null) {
      queryBuilder.andWhere('question.status = :status', { status });
    }

    // 关键词搜索
    if (keyWord) {
      queryBuilder.andWhere('(question.content LIKE :keyWord OR question.analysis LIKE :keyWord)', {
        keyWord: `%${keyWord}%`,
      });
    }

    // 排序
    queryBuilder.orderBy('question.orderNum', 'ASC');
    queryBuilder.addOrderBy('question.createTime', 'DESC');

    // 获取分页结果
    const pageResult = await this.entityRenderPage(queryBuilder, query);

    // 获取当前页题目的选项
    if (pageResult.list && pageResult.list.length > 0) {
      const questionIds = pageResult.list.map((q: any) => q.id);
      const options = await this.questionOptionEntity.find({
        where: { questionId: In(questionIds) },
        order: { orderNum: 'ASC' },
      });

      // 组装数据
      pageResult.list = pageResult.list.map((question: any) => ({
        ...question,
        options: options.filter(option => option.questionId === question.id),
      }));
    }

    return pageResult;
  }

  /**
   * 分页查询题目（包含选项）
   */
  async pageWithOptions(query: any) {
    const { provinceId, categoryId, subjectId, type, difficulty, status, keyWord } = query;

    const queryBuilder = this.questionEntity.createQueryBuilder('question');

    // 按省份筛选
    if (provinceId) {
      queryBuilder.andWhere('question.provinceId = :provinceId', { provinceId });
    }

    // 按分类筛选
    if (categoryId) {
      queryBuilder.andWhere('question.categoryId = :categoryId', { categoryId });
    }

    // 按科目筛选
    if (subjectId) {
      queryBuilder.andWhere('question.subjectId = :subjectId', { subjectId });
    }

    // 按题目类型筛选
    if (type !== undefined && type !== null) {
      queryBuilder.andWhere('question.type = :type', { type });
    }

    // 按难度筛选
    if (difficulty !== undefined && difficulty !== null) {
      queryBuilder.andWhere('question.difficulty = :difficulty', { difficulty });
    }

    // 按状态筛选
    if (status !== undefined && status !== null) {
      queryBuilder.andWhere('question.status = :status', { status });
    }

    // 关键词搜索
    if (keyWord) {
      queryBuilder.andWhere('(question.content LIKE :keyWord OR question.analysis LIKE :keyWord)', {
        keyWord: `%${keyWord}%`,
      });
    }

    // 排序
    queryBuilder.orderBy('question.orderNum', 'ASC');
    queryBuilder.addOrderBy('question.createTime', 'DESC');

    // 获取分页结果
    const pageResult = await this.entityRenderPage(queryBuilder, query);

    // 获取当前页题目的选项
    if (pageResult.list && pageResult.list.length > 0) {
      const questionIds = pageResult.list.map((q: any) => q.id);
      const options = await this.questionOptionEntity.find({
        where: { questionId: In(questionIds) },
        order: { orderNum: 'ASC' },
      });

      // 组装数据
      pageResult.list = pageResult.list.map((question: any) => ({
        ...question,
        options: options.filter(option => option.questionId === question.id),
      }));
    }

    return pageResult;
  }

  /**
   * 获取题目列表（包含选项）
   */
  async list(query: any) {
    const { provinceId, categoryId, subjectId, type, difficulty, status } = query;

    const where: any = {};

    if (provinceId) where.provinceId = provinceId;
    if (categoryId) where.categoryId = categoryId;
    if (subjectId) where.subjectId = subjectId;
    if (type !== undefined && type !== null) where.type = type;
    if (difficulty !== undefined && difficulty !== null) where.difficulty = difficulty;
    if (status !== undefined && status !== null) where.status = status;

    const questions = await this.questionEntity.find({
      where,
      order: { orderNum: 'ASC', createTime: 'DESC' },
    });

    // 获取所有题目的选项
    const questionIds = questions.map(q => q.id);
    const options = questionIds.length > 0 ? await this.questionOptionEntity.find({
      where: { questionId: In(questionIds) },
      order: { orderNum: 'ASC' },
    }) : [];

    // 组装数据
    return questions.map(question => ({
      ...question,
      options: options.filter(option => option.questionId === question.id),
    }));
  }

  /**
   * 批量导入题目
   */
  async batchImport(data: any[]) {
    const results = [];

    for (const item of data) {
      try {
        const question = await this.add(item);
        results.push({ success: true, question });
      } catch (error) {
        results.push({ success: false, error: error.message, data: item });
      }
    }

    return results;
  }

  /**
   * 根据省份和分类获取题目统计
   */
  async getStatistics(provinceId: number, categoryId?: number) {
    const queryBuilder = this.questionEntity.createQueryBuilder('question');

    queryBuilder.where('question.provinceId = :provinceId', { provinceId });

    if (categoryId) {
      queryBuilder.andWhere('question.categoryId = :categoryId', { categoryId });
    }

    const total = await queryBuilder.getCount();

    // 按题型统计
    const typeStats = await queryBuilder
      .select('question.type', 'type')
      .addSelect('COUNT(*)', 'count')
      .groupBy('question.type')
      .getRawMany();

    // 按难度统计
    const difficultyStats = await queryBuilder
      .select('question.difficulty', 'difficulty')
      .addSelect('COUNT(*)', 'count')
      .groupBy('question.difficulty')
      .getRawMany();

    return {
      total,
      typeStats,
      difficultyStats,
    };
  }

  /**
   * 查找目标省份中对应的分类和科目ID
   */
  async findTargetCategoryAndSubject(sourceCategoryId: number, sourceSubjectId: number, targetProvinceId: number) {
    let targetCategoryId = null;
    let targetSubjectId = null;

    try {
      // 1. 根据源分类ID查找源分类信息
      const sourceCategory = await this.categoryEntity.findOne({
        where: { id: sourceCategoryId }
      });

      if (sourceCategory) {
        // 2. 构建源分类的完整路径
        const sourceCategoryPath = await this.buildCategoryPath(sourceCategory);

        // 3. 在目标省份中按路径查找对应的分类
        targetCategoryId = await this.findCategoryByPath(sourceCategoryPath, targetProvinceId);
      }

      // 4. 根据源科目ID查找源科目信息
      const sourceSubject = await this.subjectEntity.findOne({
        where: { id: sourceSubjectId }
      });

      if (sourceSubject && targetCategoryId) {
        // 5. 在目标省份的对应分类下查找同名科目
        const targetSubject = await this.subjectEntity.findOne({
          where: {
            name: sourceSubject.name,
            provinceId: targetProvinceId,
            categoryId: targetCategoryId
          }
        });

        if (targetSubject) {
          targetSubjectId = targetSubject.id;
        }
      }

    } catch (error) {
      console.error('查找目标分类和科目失败:', error);
    }

    return {
      categoryId: targetCategoryId,
      subjectId: targetSubjectId
    };
  }

  /**
   * 构建分类的完整路径（从根到当前分类）
   */
  private async buildCategoryPath(category: CategoryEntity): Promise<CategoryEntity[]> {
    const path: CategoryEntity[] = [category];

    let currentCategory = category;
    while (currentCategory.parentId) {
      const parentCategory = await this.categoryEntity.findOne({
        where: { id: currentCategory.parentId }
      });

      if (parentCategory) {
        path.unshift(parentCategory);
        currentCategory = parentCategory;
      } else {
        break;
      }
    }

    return path;
  }

  /**
   * 在目标省份中按路径查找对应的分类
   */
  private async findCategoryByPath(sourcePath: CategoryEntity[], targetProvinceId: number): Promise<number | null> {
    if (sourcePath.length === 0) return null;

    // 从根分类开始查找
    let currentParentId = null;
    let targetCategoryId = null;

    for (const sourceCategory of sourcePath) {
      const targetCategory = await this.categoryEntity.findOne({
        where: {
          name: sourceCategory.name,
          regionId: targetProvinceId,
          parentId: currentParentId
        }
      });

      if (!targetCategory) {
        console.log(`在目标省份 ${targetProvinceId} 中未找到分类: ${sourceCategory.name}`);
        return null;
      }

      currentParentId = targetCategory.id;
      targetCategoryId = targetCategory.id;
    }

    return targetCategoryId;
  }
}
