import { Provide } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { QuestionEntity } from '../entity/question';
import { CategoryEntity } from '../../category/entity/category';
import { SubjectEntity } from '../../subject/entity/subject';

/**
 * 题目服务
 */
@Provide()
export class QuestionService extends BaseService {
  @InjectEntityModel(QuestionEntity)
  questionEntity: Repository<QuestionEntity>;

  @InjectEntityModel(CategoryEntity)
  categoryEntity: Repository<CategoryEntity>;

  @InjectEntityModel(SubjectEntity)
  subjectEntity: Repository<SubjectEntity>;

  // 使用框架自带的 add、update、delete 方法，无需重写

  /**
   * 获取题目详情（包含选项）- 重写框架方法以支持JSON选项解析
   */
  async info(param: any) {
    const { id } = param;
    const question = await this.questionEntity.findOne({ where: { id } });
    if (!question) {
      return null;
    }

    // 解析JSON选项
    let options = [];
    if (question.options) {
      try {
        options = JSON.parse(question.options);
      } catch (error) {
        console.error('解析选项JSON失败:', error);
        options = [];
      }
    }

    return {
      ...question,
      options,
    };
  }

  /**
   * 分页查询题目（包含选项）- 重写框架方法以支持JSON选项解析
   */
  async page(query: any) {
    // 使用框架的分页查询
    const queryBuilder = this.questionEntity.createQueryBuilder('question');
    const pageResult = await this.entityRenderPage(queryBuilder, query);

    // 解析JSON选项
    if (pageResult.list && pageResult.list.length > 0) {
      pageResult.list = pageResult.list.map((question: any) => {
        let options = [];
        if (question.options) {
          try {
            options = JSON.parse(question.options);
          } catch (error) {
            console.error('解析选项JSON失败:', error);
            options = [];
          }
        }
        return {
          ...question,
          options,
        };
      });
    }

    return pageResult;
  }



  /**
   * 获取题目列表（包含选项）- 重写框架方法以支持JSON选项解析
   */
  async list(query: any) {
    const questions = await this.questionEntity.find({
      where: this.getListWhere(query),
      order: { orderNum: 'ASC', createTime: 'DESC' },
    });

    // 解析JSON选项
    return questions.map((question: any) => {
      let options = [];
      if (question.options) {
        try {
          options = JSON.parse(question.options);
        } catch (error) {
          console.error('解析选项JSON失败:', error);
          options = [];
        }
      }
      return {
        ...question,
        options,
      };
    });
  }

  /**
   * 构建查询条件
   */
  private getListWhere(query: any) {
    const { provinceId, categoryId, subjectId, type, difficulty, status } = query;
    const where: any = {};

    if (provinceId) where.provinceId = provinceId;
    if (categoryId) where.categoryId = categoryId;
    if (subjectId) where.subjectId = subjectId;
    if (type !== undefined && type !== null) where.type = type;
    if (difficulty !== undefined && difficulty !== null) where.difficulty = difficulty;
    if (status !== undefined && status !== null) where.status = status;

    return where;
  }

  /**
   * 批量导入题目
   */
  async batchImport(data: any[]) {
    const results = [];

    for (const item of data) {
      try {
        const question = await this.add(item);
        results.push({ success: true, question });
      } catch (error) {
        results.push({ success: false, error: error.message, data: item });
      }
    }

    return results;
  }

  /**
   * 根据省份和分类获取题目统计
   */
  async getStatistics(provinceId: number, categoryId?: number) {
    const queryBuilder = this.questionEntity.createQueryBuilder('question');

    queryBuilder.where('question.provinceId = :provinceId', { provinceId });

    if (categoryId) {
      queryBuilder.andWhere('question.categoryId = :categoryId', { categoryId });
    }

    const total = await queryBuilder.getCount();

    // 按题型统计
    const typeStats = await queryBuilder
      .select('question.type', 'type')
      .addSelect('COUNT(*)', 'count')
      .groupBy('question.type')
      .getRawMany();

    // 按难度统计
    const difficultyStats = await queryBuilder
      .select('question.difficulty', 'difficulty')
      .addSelect('COUNT(*)', 'count')
      .groupBy('question.difficulty')
      .getRawMany();

    return {
      total,
      typeStats,
      difficultyStats,
    };
  }

  /**
   * 查找目标省份中对应的分类和科目ID
   */
  async findTargetCategoryAndSubject(sourceCategoryId: number, sourceSubjectId: number, targetProvinceId: number) {
    let targetCategoryId = null;
    let targetSubjectId = null;

    try {
      // 1. 根据源分类ID查找源分类信息
      const sourceCategory = await this.categoryEntity.findOne({
        where: { id: sourceCategoryId }
      });

      if (sourceCategory) {
        // 2. 构建源分类的完整路径
        const sourceCategoryPath = await this.buildCategoryPath(sourceCategory);

        // 3. 在目标省份中按路径查找对应的分类
        targetCategoryId = await this.findCategoryByPath(sourceCategoryPath, targetProvinceId);
      }

      // 4. 根据源科目ID查找源科目信息
      const sourceSubject = await this.subjectEntity.findOne({
        where: { id: sourceSubjectId }
      });

      if (sourceSubject && targetCategoryId) {
        // 5. 在目标省份的对应分类下查找同名科目
        const targetSubject = await this.subjectEntity.findOne({
          where: {
            name: sourceSubject.name,
            provinceId: targetProvinceId,
            categoryId: targetCategoryId
          }
        });

        if (targetSubject) {
          targetSubjectId = targetSubject.id;
        }
      }

    } catch (error) {
      console.error('查找目标分类和科目失败:', error);
    }

    return {
      categoryId: targetCategoryId,
      subjectId: targetSubjectId
    };
  }

  /**
   * 构建分类的完整路径（从根到当前分类）
   */
  private async buildCategoryPath(category: CategoryEntity): Promise<CategoryEntity[]> {
    const path: CategoryEntity[] = [category];

    let currentCategory = category;
    while (currentCategory.parentId) {
      const parentCategory = await this.categoryEntity.findOne({
        where: { id: currentCategory.parentId }
      });

      if (parentCategory) {
        path.unshift(parentCategory);
        currentCategory = parentCategory;
      } else {
        break;
      }
    }

    return path;
  }

  /**
   * 在目标省份中按路径查找对应的分类
   */
  private async findCategoryByPath(sourcePath: CategoryEntity[], targetProvinceId: number): Promise<number | null> {
    if (sourcePath.length === 0) return null;

    // 从根分类开始查找
    let currentParentId = null;
    let targetCategoryId = null;

    for (const sourceCategory of sourcePath) {
      const targetCategory = await this.categoryEntity.findOne({
        where: {
          name: sourceCategory.name,
          regionId: targetProvinceId,
          parentId: currentParentId
        }
      });

      if (!targetCategory) {
        console.log(`在目标省份 ${targetProvinceId} 中未找到分类: ${sourceCategory.name}`);
        return null;
      }

      currentParentId = targetCategory.id;
      targetCategoryId = targetCategory.id;
    }

    return targetCategoryId;
  }

  /**
   * 记录答题结果并更新统计
   */
  async recordAnswer(questionId: number, isCorrect: boolean) {
    const question = await this.questionEntity.findOne({ where: { id: questionId } });
    if (!question) {
      throw new Error('题目不存在');
    }

    // 更新统计数据
    const newAnswerCount = question.answerCount + 1;
    const newCorrectCount = question.correctCount + (isCorrect ? 1 : 0);
    const newCorrectRate = (newCorrectCount / newAnswerCount) * 100;

    await this.questionEntity.update(questionId, {
      answerCount: newAnswerCount,
      correctCount: newCorrectCount,
      correctRate: newCorrectRate,
    });

    // 根据正确率自动调整难度
    await this.adjustDifficultyByCorrectRate(questionId, newCorrectRate);

    return {
      answerCount: newAnswerCount,
      correctCount: newCorrectCount,
      correctRate: newCorrectRate,
    };
  }

  /**
   * 根据正确率自动调整题目难度
   */
  private async adjustDifficultyByCorrectRate(questionId: number, correctRate: number) {
    // 只有答题次数达到一定数量才调整难度，避免样本太小
    const question = await this.questionEntity.findOne({ where: { id: questionId } });
    if (!question || question.answerCount < 10) {
      return;
    }

    let newDifficulty = question.difficulty;

    // 根据正确率调整难度
    if (correctRate >= 80) {
      // 正确率高于80%，题目太简单，提升难度
      newDifficulty = Math.min(2, question.difficulty + 1); // 最高困难
    } else if (correctRate <= 30) {
      // 正确率低于30%，题目太难，降低难度
      newDifficulty = Math.max(0, question.difficulty - 1); // 最低简单
    }

    // 如果难度有变化，更新题目
    if (newDifficulty !== question.difficulty) {
      await this.questionEntity.update(questionId, {
        difficulty: newDifficulty,
      });
      console.log(`题目 ${questionId} 难度从 ${question.difficulty} 调整为 ${newDifficulty}，正确率: ${correctRate}%`);
    }
  }

  /**
   * 获取题目答题统计
   */
  async getAnswerStatistics(questionId: number) {
    const question = await this.questionEntity.findOne({
      where: { id: questionId },
      select: ['id', 'content', 'answerCount', 'correctCount', 'correctRate', 'difficulty']
    });

    if (!question) {
      throw new Error('题目不存在');
    }

    return question;
  }

  /**
   * 批量获取题目统计（用于分析）
   */
  async getBatchStatistics(provinceId: number, categoryId?: number) {
    const queryBuilder = this.questionEntity.createQueryBuilder('question');

    queryBuilder.where('question.provinceId = :provinceId', { provinceId });

    if (categoryId) {
      queryBuilder.andWhere('question.categoryId = :categoryId', { categoryId });
    }

    queryBuilder.select([
      'question.id',
      'question.content',
      'question.type',
      'question.difficulty',
      'question.answerCount',
      'question.correctCount',
      'question.correctRate'
    ]);

    queryBuilder.orderBy('question.correctRate', 'ASC'); // 按正确率排序，最难的在前面

    return await queryBuilder.getMany();
  }
}
