import { Inject, Provide } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { QuestionService } from './question';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { SubjectEntity } from '../../subject/entity/subject';

/**
 * 题目导入服务
 */
@Provide()
export class QuestionImportService extends BaseService {
  @Inject()
  questionService: QuestionService;

  @InjectEntityModel(SubjectEntity)
  subjectEntity: Repository<SubjectEntity>;

  /**
   * 根据省份ID和分类ID查找对应的科目
   */
  async findSubjectByProvinceAndCategory(provinceId: number, categoryId: number): Promise<SubjectEntity | null> {
    const subject = await this.subjectEntity.findOne({
      where: {
        provinceId,
        categoryId,
        status: 1 // 只查找启用的科目
      }
    });

    return subject;
  }

  /**
   * 解析选项字符串
   */
  private parseOptions(optionsStr: string, correctAnswer: string, type: number, categoryId?: number) {
    const options = [];

    if (type === 2) { // 判断题
      options.push({
        label: 'A',
        content: '正确',
        image: '',
        isCorrect: this.isCorrectAnswer(correctAnswer, 'A'),
        orderNum: 1,
      });
      options.push({
        label: 'B',
        content: '错误',
        image: '',
        isCorrect: this.isCorrectAnswer(correctAnswer, 'B'),
        orderNum: 2,
      });
    } else {
      // 单选题和多选题
      // 处理多种分隔符：\n, \\n, 换行符
      let optionTexts = [];
      if (optionsStr.includes('\\n')) {
        optionTexts = optionsStr.split('\\n');
      } else if (optionsStr.includes('\n')) {
        optionTexts = optionsStr.split('\n');
      } else {
        // 如果没有分隔符，可能是单个选项或者用其他方式分隔
        optionTexts = [optionsStr];
      }

      const labels = ['A', 'B', 'C', 'D', 'E', 'F'];

      optionTexts.forEach((text, index) => {
        if (index < labels.length && text && text.trim()) {
          const label = labels[index];
          let cleanText = text.trim();

          // 移除选项标签（支持多种格式：A. A、 A： A) A】等）
          const labelPatterns = [
            new RegExp(`^[${label}][\\.、：）】]\\s*`, 'i'),  // A. A、 A： A) A】
            new RegExp(`^[${label}]\\s*[\\.、：）】]\\s*`, 'i'), // A . A 、 A ： A ) A 】
            new RegExp(`^[${label}]\\s+`, 'i'),  // A 后面跟空格
          ];

          for (const pattern of labelPatterns) {
            if (pattern.test(cleanText)) {
              cleanText = cleanText.replace(pattern, '');
              break;
            }
          }

          // 如果还没有移除标签，尝试通用模式
          if (cleanText === text.trim()) {
            // 移除开头的任何字母+标点符号组合
            cleanText = cleanText.replace(/^[A-F][\\.、：）】\s]+/i, '');
          }

          // 处理图片
          const processedOption = this.processContentWithImages(cleanText, categoryId);

          options.push({
            label,
            content: processedOption.content || cleanText,
            image: processedOption.image || '',
            isCorrect: this.isCorrectAnswer(correctAnswer, label),
            orderNum: index + 1,
          });
        }
      });
    }

    return options;
  }

  /**
   * 判断是否为正确答案
   */
  private isCorrectAnswer(correctAnswer: string, label: string): boolean {
    if (!correctAnswer) return false;

    // 处理多选题的逗号分隔（A,C 或 A，C）
    const answers = correctAnswer.toUpperCase()
      .split(/[,，]/)  // 支持中英文逗号
      .map(answer => answer.trim())
      .filter(answer => answer.length > 0);

    return answers.includes(label.toUpperCase());
  }

  /**
   * 批量导入题目（Excel格式）
   */
  async importFromExcel(excelData: any[], provinceId: number, categoryId?: number) {
    // 首先查找对应的科目
    if (!categoryId) {
      throw new Error('分类ID不能为空');
    }

    const subject = await this.findSubjectByProvinceAndCategory(provinceId, categoryId);
    if (!subject) {
      throw new Error(`在省份ID ${provinceId} 的分类ID ${categoryId} 下未找到对应的科目，请先创建科目`);
    }

    console.log(`找到科目: ${subject.name} (ID: ${subject.id})`);

    const results = [];

    for (let i = 0; i < excelData.length; i++) {
      const row = excelData[i];

      try {
        const questionData = await this.processRowData(row, provinceId, categoryId, subject.id);
        const question = await this.questionService.add(questionData);

        results.push({
          success: true,
          row: i + 1,
          questionId: question.id,
          subjectId: subject.id,
          subjectName: subject.name,
        });

      } catch (error) {
        results.push({
          success: false,
          row: i + 1,
          error: error.message,
          data: row,
        });
      }
    }

    return results;
  }

  /**
   * 从行数据中获取字段值（支持多种列名）
   */
  private getFieldValue(row: any, fieldNames: string[]): any {
    for (const fieldName of fieldNames) {
      if (row[fieldName] !== undefined && row[fieldName] !== null && row[fieldName] !== '') {
        return row[fieldName];
      }
    }
    return null;
  }

  /**
   * 导入CSV格式数据
   */
  async importFromCSV(csvData: string, provinceId: number, categoryId?: number) {
    // 首先查找对应的科目
    if (!categoryId) {
      throw new Error('分类ID不能为空');
    }

    const subject = await this.findSubjectByProvinceAndCategory(provinceId, categoryId);
    if (!subject) {
      throw new Error(`在省份ID ${provinceId} 的分类ID ${categoryId} 下未找到对应的科目，请先创建科目`);
    }

    const lines = csvData.trim().split('\n');
    const results = [];

    // 假设第一行是标题行
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));

    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      try {
        // 解析CSV行
        const values = this.parseCSVLine(line);
        const row: any = {};

        // 将值映射到对应的列名
        headers.forEach((header, index) => {
          if (values[index] !== undefined) {
            row[header] = values[index];
          }
        });

        // 使用Excel导入逻辑处理数据
        const questionData = await this.processRowData(row, provinceId, categoryId, subject.id);
        const question = await this.questionService.add(questionData);

        results.push({
          success: true,
          row: i,
          questionId: question.id,
          subjectId: subject.id,
          subjectName: subject.name,
        });

      } catch (error) {
        results.push({
          success: false,
          row: i,
          error: error.message,
          data: line,
        });
      }
    }

    return results;
  }

  /**
   * 解析CSV行（处理引号和逗号）
   */
  private parseCSVLine(line: string): string[] {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
      const char = line[i];

      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }

    result.push(current.trim());
    return result;
  }

  /**
   * 处理行数据（提取公共逻辑）
   */
  private async processRowData(row: any, provinceId: number, categoryId?: number, subjectId?: number) {
    const type = this.getFieldValue(row, ['题型', 'type', '题目类型']);
    const content = this.getFieldValue(row, ['题目', 'content', '题目内容', '问题']);
    const image = this.getFieldValue(row, ['题目图片', 'image', '图片']);
    const options = this.getFieldValue(row, ['答案选项', 'options', '选项', '答案']);
    const correctAnswer = this.getFieldValue(row, ['正确答案', 'correctAnswer', '答案', '正确选项']);
    const analysis = this.getFieldValue(row, ['答题解析', 'analysis', '解析', '说明']);
    const difficulty = this.getFieldValue(row, ['难度', 'difficulty', '难度级别']);
    const score = this.getFieldValue(row, ['分数', 'score', '题目分数', '分值']);

    // 验证必填字段
    if (!type || !content) {
      throw new Error('题型和题目内容不能为空');
    }

    // 处理题目内容中的图片
    const processedContent = this.processContentWithImages(content.toString().trim(), categoryId);

    return {
      type: parseInt(type) - 1, // 转换为0-based索引
      content: processedContent.content,
      image: processedContent.image || (image ? image.toString().trim() : ''),
      analysis: analysis ? analysis.toString().trim() : '',
      difficulty: difficulty ? parseInt(difficulty) - 1 : 0, // 默认简单
      score: score ? parseFloat(score.toString()) : 1.0, // 默认1分
      provinceId,
      categoryId,
      subjectId, // 添加科目ID
      status: 1,
      orderNum: 0,
      options: this.parseOptions(
        options ? options.toString() : '',
        correctAnswer ? correctAnswer.toString() : '',
        parseInt(type) - 1,
        categoryId
      ),
    };
  }

  /**
   * 处理题目内容中的图片标签
   */
  private processContentWithImages(content: string, categoryId?: number) {
    // 匹配图片标签格式：< img src=filename.png  /> (无引号格式)
    // 支持标签前后有空格，src属性值无引号
    const imgRegex = /<\s*img\s+src\s*=\s*([^\s>]+)\s*[^>]*\s*\/?>/gi;

    let extractedImage = '';
    let cleanContent = content;

    console.log('原始内容:', content);

    // 提取图片并从内容中移除图片标签
    const matches = content.match(imgRegex);
    console.log('匹配到的图片标签:', matches);

    if (matches && matches.length > 0) {
      // 提取第一个图片的文件名 (无引号格式)
      const srcMatch = matches[0].match(/src\s*=\s*([^\s>]+)/i);
      console.log('提取的src匹配:', srcMatch);

      if (srcMatch && srcMatch[1]) {
        const fileName = srcMatch[1].trim();
        console.log('提取的文件名:', fileName);

        if (fileName) {
          // 构建完整的图片URL路径
          extractedImage = this.buildImageUrl(fileName, categoryId);
          console.log('构建的图片URL:', extractedImage);
        }
      }

      // 从题目内容中移除所有图片标签，只保留纯文本
      cleanContent = content.replace(imgRegex, '').trim();
      console.log('清理后的内容:', cleanContent);
    }

    return {
      content: cleanContent,
      image: extractedImage
    };
  }

  /**
   * 构建图片URL路径
   */
  private buildImageUrl(fileName: string, categoryId?: number): string {
    // 如果已经是完整URL，直接返回
    if (fileName.startsWith('http://') || fileName.startsWith('https://') || fileName.startsWith('/')) {
      return fileName;
    }

    // 构建相对于上传目录的路径
    // 根据控制器中的路径配置：/upload/images/category_{categoryId}/
    if (categoryId) {
      return `/upload/images/category_${categoryId}/${fileName}`;
    }

    // 默认路径（兼容旧数据）
    return `/upload/images/${fileName}`;
  }

  /**
   * 验证Excel/CSV数据格式
   */
  validateImportData(data: any[]) {
    const errors = [];

    for (let i = 0; i < data.length; i++) {
      const row = data[i];

      try {
        const type = this.getFieldValue(row, ['题型', 'type', '题目类型']);
        const content = this.getFieldValue(row, ['题目', 'content', '题目内容', '问题']);
        const difficulty = this.getFieldValue(row, ['难度', 'difficulty', '难度级别']);

        // 验证题目类型
        if (!type || isNaN(parseInt(type)) || parseInt(type) < 1 || parseInt(type) > 3) {
          errors.push({
            row: i + 1,
            error: '题目类型无效，应为1(单选)、2(多选)或3(判断)',
          });
        }

        // 验证题目内容
        if (!content || !content.toString().trim()) {
          errors.push({
            row: i + 1,
            error: '题目内容不能为空',
          });
        }

        // 验证难度
        if (difficulty && (isNaN(parseInt(difficulty)) || parseInt(difficulty) < 1 || parseInt(difficulty) > 3)) {
          errors.push({
            row: i + 1,
            error: '难度级别无效，应为1(简单)、2(中等)或3(困难)',
          });
        }

      } catch (error) {
        errors.push({
          row: i + 1,
          error: `数据解析错误: ${error.message}`,
        });
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      totalRows: data.length,
    };
  }

  /**
   * 导出题目数据到Excel
   */
  async exportToExcel(provinceId: number, categoryId?: number) {
    // 构建查询条件
    const query: any = { provinceId };
    if (categoryId) {
      query.categoryId = categoryId;
    }

    // 获取题目列表（包含选项）
    const questions = await this.questionService.list(query);

    // 转换数据格式
    const exportData = questions.map((question: any) => {
      // 处理选项
      let optionsText = '';
      if (question.options && question.options.length > 0) {
        optionsText = question.options
          .sort((a: any, b: any) => a.orderNum - b.orderNum)
          .map((option: any) => `${option.label}. ${option.content}`)
          .join('\n');
      }

      // 处理正确答案
      let correctAnswer = '';
      if (question.options && question.options.length > 0) {
        const correctOptions = question.options
          .filter((option: any) => option.isCorrect)
          .map((option: any) => option.label);
        correctAnswer = correctOptions.join(',');
      }

      return {
        '题型': question.type + 1, // 转换为1-based索引
        '题目内容': question.content,
        '题目图片': question.image || '',
        '答案选项': optionsText,
        '正确答案': correctAnswer,
        '答题解析': question.analysis || '',
        '分数': question.score || 1.0,
        '难度级别': question.difficulty + 1, // 转换为1-based索引
      };
    });

    return exportData;
  }
}
