import { Provide } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { QuestionEntity } from './entity/question';
import { QuestionOptionEntity } from './entity/option';

/**
 * 题库模块初始化服务
 */
@Provide()
export class QuestionInitService extends BaseService {
  @InjectEntityModel(QuestionEntity)
  questionEntity: Repository<QuestionEntity>;

  @InjectEntityModel(QuestionOptionEntity)
  questionOptionEntity: Repository<QuestionOptionEntity>;

  /**
   * 初始化题库模块
   */
  async init() {
    // 确保数据库表已创建
    // TypeORM会自动处理表的创建，这里只是一个占位方法
    console.log('题库模块初始化完成');
  }
}
