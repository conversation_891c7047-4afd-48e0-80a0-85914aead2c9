import { Inject, Controller, Get, Post, Body, Query, Param } from '@midwayjs/core';
import { CoolController, BaseController } from '@cool-midway/core';
import { QuestionVersionEntity } from '../../entity/question-version';
import { QuestionVersionService } from '../../service/version';

/**
 * 题库版本管理
 */
@Controller('/admin/question/version')
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: QuestionVersionEntity,
  service: QuestionVersionService,
})
export class AdminQuestionVersionController extends BaseController {
  @Inject()
  questionVersionService: QuestionVersionService;

  /**
   * 创建新版本
   */
  @Post('/create')
  async createVersion(@Body() body: {
    name: string;
    description?: string;
    subjectId: number;
    provinceId: number;
    categoryId: number;
  }) {
    const { name, description, subjectId, provinceId, categoryId } = body;

    // 从当前用户信息获取创建者信息
    const creatorId = 1; // 临时使用固定值，实际应该从用户上下文获取
    const creatorName = 'Admin'; // 临时使用固定值

    const result = await this.questionVersionService.createVersion({
      name,
      description,
      subjectId,
      provinceId,
      categoryId,
      creatorId,
      creatorName
    });

    return this.ok(result);
  }

  /**
   * 复制题目到新版本
   */
  @Post('/copyQuestions')
  async copyQuestions(@Body() body: {
    sourceVersionId: number;
    targetVersionId: number;
  }) {
    const { sourceVersionId, targetVersionId } = body;
    const result = await this.questionVersionService.copyQuestionsToVersion(
      sourceVersionId,
      targetVersionId
    );
    return this.ok(result);
  }

  /**
   * 发布版本
   */
  @Post('/publish/:id')
  async publishVersion(@Param('id') id: number) {
    const reviewerId = this.ctx.user.id;
    const reviewerName = this.ctx.user.name || this.ctx.user.username;

    const result = await this.questionVersionService.publishVersion(
      id,
      reviewerId,
      reviewerName
    );
    return this.ok(result);
  }

  /**
   * 激活版本（平滑切换）
   */
  @Post('/activate/:id')
  async activateVersion(
    @Param('id') id: number,
    @Body() body: { effectiveTime?: string }
  ) {
    const effectiveTime = body.effectiveTime ? new Date(body.effectiveTime) : undefined;
    const result = await this.questionVersionService.activateVersion(id, effectiveTime);
    return this.ok(result);
  }

  /**
   * 获取科目的版本列表
   */
  @Get('/list/:subjectId')
  async getVersionList(@Param('subjectId') subjectId: number) {
    const result = await this.questionVersionService.getVersionList(subjectId);
    return this.ok(result);
  }

  /**
   * 回滚版本
   */
  @Post('/rollback')
  async rollbackVersion(@Body() body: {
    currentVersionId: number;
    targetVersionId: number;
  }) {
    const { currentVersionId, targetVersionId } = body;
    const result = await this.questionVersionService.rollbackVersion(
      currentVersionId,
      targetVersionId
    );
    return this.ok(result);
  }

  /**
   * 更新版本状态
   */
  @Post('/updateStatus/:id')
  async updateStatus(
    @Param('id') id: number,
    @Body() body: { status: number }
  ) {
    await this.questionVersionService.versionEntity.update(id, {
      status: body.status
    });
    return this.ok('状态更新成功');
  }

  /**
   * 获取版本统计信息
   */
  @Get('/statistics/:id')
  async getStatistics(@Param('id') id: number) {
    const version = await this.questionVersionService.versionEntity.findOne({
      where: { id }
    });

    if (!version) {
      return this.fail('版本不存在');
    }

    // 更新题目数量
    const questionCount = await this.questionVersionService.updateQuestionCount(id);

    const statistics = {
      ...version,
      questionCount,
      // 可以添加更多统计信息
      // 如：各题型数量、难度分布等
    };

    return this.ok(statistics);
  }
}
