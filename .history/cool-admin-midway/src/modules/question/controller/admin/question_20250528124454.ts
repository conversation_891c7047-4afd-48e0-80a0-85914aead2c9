import { Inject, Controller, Get, Post, Body, Query, Param, Files, Fields, App } from '@midwayjs/core';
import { CoolController, BaseController } from '@cool-midway/core';
import { QuestionEntity } from '../../entity/question';
import { QuestionService } from '../../service/question';
import { QuestionImportService } from '../../service/import';
import * as fs from 'fs';
import * as path from 'path';
import * as moment from 'moment';
import { v1 as uuid } from 'uuid';
import { CoolCommException } from '@cool-midway/core';

/**
 * 题目管理
 */
@Controller('/admin/question')
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: QuestionEntity,
  listQueryOp: {
    fieldEq: ['provinceId', 'categoryId', 'subjectId', 'type', 'difficulty', 'status'],
    keyWordLikeFields: ['content', 'analysis'],
    addOrderBy: {
      orderNum: 'ASC',
      createTime: 'DESC',
    },
  },
  pageQueryOp: {
    fieldEq: ['provinceId', 'categoryId', 'subjectId', 'type', 'difficulty', 'status'],
    keyWordLikeFields: ['content', 'analysis'],
    addOrderBy: {
      orderNum: 'ASC',
      createTime: 'DESC',
    },
  },
})
export class AdminQuestionController extends BaseController {
  @Inject()
  questionService: QuestionService;

  @Inject()
  questionImportService: QuestionImportService;

  @App()
  app: any;

  /**
   * 新增题目（包含选项）
   */
  @Post('/addWithOptions')
  async addWithOptions(@Body() body: any) {
    const result = await this.questionService.add(body);
    return this.ok(result);
  }

  /**
   * 更新题目（包含选项）
   */
  @Post('/updateWithOptions')
  async updateWithOptions(@Body() body: any) {
    const result = await this.questionService.updateWithOptions(body);
    return this.ok(result);
  }

  /**
   * 获取题目详情（包含选项）
   */
  @Get('/infoWithOptions')
  async infoWithOptions(@Query('id') id: number) {
    console.log('=== infoWithOptions 接口被调用 ===');
    console.log('题目ID:', id);
    const result = await this.questionService.info({ id });
    console.log('Service返回的结果:', result);
    console.log('选项数据:', result.options);
    if (result.options && result.options.length > 0) {
      console.log('第一个选项详情:', result.options[0]);
    }
    return this.ok(result);
  }

  /**
   * 获取题目列表（包含选项）
   */
  @Get('/listWithOptions')
  async listWithOptions(@Query() query: any) {
    const result = await this.questionService.list(query);
    return this.ok(result);
  }

  /**
   * 分页查询题目（包含选项）
   */
  @Post('/pageWithOptions')
  async pageWithOptions(@Body() body: any) {
    const result = await this.questionService.pageWithOptions(body);
    return this.ok(result);
  }

  /**
   * 批量导入题目
   */
  @Post('/batchImport')
  async batchImport(@Body() body: { data: any[] }) {
    const result = await this.questionService.batchImport(body.data);
    return this.ok(result);
  }

  /**
   * 验证导入数据格式
   */
  @Post('/validateImportData')
  async validateImportData(@Body() body: { data: any[] }) {
    const result = this.questionImportService.validateImportData(body.data);
    return this.ok(result);
  }

  /**
   * 导入Excel数据
   */
  @Post('/importExcel')
  async importExcel(@Body() body: { data: any[]; provinceId: number; categoryId?: number }) {
    const { data, provinceId, categoryId } = body;
    const result = await this.questionImportService.importFromExcel(data, provinceId, categoryId);
    return this.ok(result);
  }

  /**
   * 导入CSV数据
   */
  @Post('/importCSV')
  async importCSV(@Body() body: { data: string; provinceId: number; categoryId?: number }) {
    const { data, provinceId, categoryId } = body;
    const result = await this.questionImportService.importFromCSV(data, provinceId, categoryId);
    return this.ok(result);
  }

  /**
   * 获取题目统计信息
   */
  @Get('/statistics')
  async getStatistics(@Query() query: { provinceId: number; categoryId?: number }) {
    const result = await this.questionService.getStatistics(query.provinceId, query.categoryId);
    return this.ok(result);
  }

  /**
   * 根据省份获取题目树形结构
   */
  @Get('/tree/:provinceId')
  async getQuestionTree(@Param('provinceId') provinceId: number) {
    const questions = await this.questionService.list({ provinceId, status: 1 });

    // 按分类分组
    const categoryMap = new Map();

    questions.forEach(question => {
      const categoryId = question.categoryId || 0;
      if (!categoryMap.has(categoryId)) {
        categoryMap.set(categoryId, []);
      }
      categoryMap.get(categoryId).push(question);
    });

    const tree = Array.from(categoryMap.entries()).map(([categoryId, questions]) => ({
      categoryId,
      questions,
      count: questions.length,
    }));

    return this.ok(tree);
  }

  /**
   * 复制题目到其他省份
   */
  @Post('/copyToProvince')
  async copyToProvince(@Body() body: { questionIds: number[]; targetProvinceId: number }) {
    const { questionIds, targetProvinceId } = body;

    const results = [];

    for (const questionId of questionIds) {
      try {
        // 获取原题目信息
        const originalQuestion = await this.questionService.info({ id: questionId });
        if (!originalQuestion) {
          results.push({ success: false, questionId, error: '题目不存在' });
          continue;
        }

        // 查找目标省份中对应的分类ID和科目ID
        const mappingResult = await this.questionService.findTargetCategoryAndSubject(
          originalQuestion.categoryId,
          originalQuestion.subjectId,
          targetProvinceId
        );

        if (!mappingResult.categoryId) {
          results.push({
            success: false,
            questionId,
            error: `目标省份未找到对应的分类`
          });
          continue;
        }

        if (!mappingResult.subjectId) {
          results.push({
            success: false,
            questionId,
            error: `目标省份未找到对应的科目`
          });
          continue;
        }

        // 创建新题目
        const newQuestionData = {
          ...originalQuestion,
          provinceId: targetProvinceId,
          categoryId: mappingResult.categoryId,
          subjectId: mappingResult.subjectId,
          // 重置答题统计数据
          answerCount: 0,
          correctCount: 0,
          correctRate: 0,
        };
        delete newQuestionData.id;
        delete newQuestionData.createTime;
        delete newQuestionData.updateTime;

        // 处理选项（JSON格式）
        if (newQuestionData.options) {
          // 如果选项是数组格式（从info接口返回的已解析数据），转换为JSON字符串
          if (Array.isArray(newQuestionData.options)) {
            // 清理选项数据，移除不需要的字段
            const cleanOptions = newQuestionData.options.map((option: any) => {
              const { id, questionId, createTime, updateTime, ...optionData } = option;
              return optionData;
            });
            (newQuestionData as any).options = JSON.stringify(cleanOptions);
          }
          // 如果已经是JSON字符串，直接使用
        }

        const newQuestion = await this.questionService.add(newQuestionData);
        results.push({
          success: true,
          questionId,
          newQuestionId: newQuestion.id,
          mappedCategoryId: mappingResult.categoryId,
          mappedSubjectId: mappingResult.subjectId
        });
      } catch (error) {
        results.push({ success: false, questionId, error: error.message });
      }
    }

    return this.ok(results);
  }

  /**
   * 导出题目数据
   */
  @Post('/export')
  async export(@Body() body: { provinceId: number; categoryId?: number }) {
    const { provinceId, categoryId } = body;
    const result = await this.questionImportService.exportToExcel(provinceId, categoryId);
    return this.ok(result);
  }

  /**
   * 重写删除方法，确保级联删除选项
   */
  @Post('/delete')
  async deleteQuestions(@Body() body: { ids: number[] }) {
    const { ids } = body;
    await this.questionService.delete(ids);
    return this.ok();
  }

  /**
   * 上传题目图片
   */
  @Post('/uploadImage')
  async uploadImage(@Files() files: any, @Fields() fields: any, @Ctx() ctx: any) {
    try {
      const { categoryId } = fields;

      if (!files || files.length === 0) {
        throw new CoolCommException('上传文件为空');
      }

      const file = files[0];
      if (!file) {
        throw new CoolCommException('上传文件为空');
      }

      // 验证文件类型
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.mimeType)) {
        throw new CoolCommException('只支持图片文件格式 (JPG, PNG, GIF, WEBP)');
      }

      // 验证文件大小 (2MB)
      const maxSize = 2 * 1024 * 1024;
      if (file.data.length > maxSize) {
        throw new CoolCommException('文件大小不能超过 2MB');
      }

      // 获取上传路径配置
      const uploadPath = this.app.getConfig('staticFile.dirs.static.dir');

      // 创建题目图片文件夹路径
      const imageDir = categoryId
        ? path.join(uploadPath, 'images', `category_${categoryId}`)
        : path.join(uploadPath, 'images', 'questions');

      // 确保目录存在
      if (!fs.existsSync(imageDir)) {
        fs.mkdirSync(imageDir, { recursive: true });
      }

      // 生成唯一文件名
      const extension = path.extname(file.filename);
      const fileName = `${uuid()}${extension}`;
      const filePath = path.join(imageDir, fileName);

      // 写入文件
      fs.writeFileSync(filePath, file.data);

      // 返回访问URL - 使用相对路径，让前端自动处理域名
      const url = categoryId
        ? `/upload/images/category_${categoryId}/${fileName}`
        : `/upload/images/questions/${fileName}`;

      return this.ok({
        url,
        filename: fileName,
        size: file.data.length,
        categoryId: categoryId ? parseInt(categoryId) : null
      });

    } catch (error) {
      console.error('题目图片上传失败:', error);
      throw new CoolCommException(error.message || '题目图片上传失败');
    }
  }

  /**
   * 上传分类图片
   */
  @Post('/uploadCategoryImage')
  async uploadCategoryImage(@Files() files: any, @Fields() fields: any) {
    try {
      const { categoryId } = fields;

      if (!categoryId) {
        throw new CoolCommException('缺少分类ID');
      }

      if (!files || files.length === 0) {
        throw new CoolCommException('上传文件为空');
      }

      const file = files[0];
      if (!file) {
        throw new CoolCommException('上传文件为空');
      }

      // 验证文件类型
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.mimeType)) {
        throw new CoolCommException('只支持图片文件格式 (JPG, PNG, GIF, WEBP)');
      }

      // 验证文件大小 (2MB)
      const maxSize = 2 * 1024 * 1024;
      if (file.data.length > maxSize) {
        throw new CoolCommException('文件大小不能超过 2MB');
      }

      // 获取上传路径配置
      const uploadPath = this.app.getConfig('staticFile.dirs.static.dir');

      // 创建分类文件夹路径
      const categoryDir = path.join(uploadPath, 'images', `category_${categoryId}`);

      // 确保目录存在
      if (!fs.existsSync(categoryDir)) {
        fs.mkdirSync(categoryDir, { recursive: true });
      }

      // 获取文件扩展名
      const extension = path.extname(file.filename);

      // 使用原始文件名（保持与导入数据一致）
      const fileName = file.filename;
      const filePath = path.join(categoryDir, fileName);

      // 写入文件
      fs.writeFileSync(filePath, file.data);

      // 返回访问URL
      const domain = this.app.getConfig('plugin.hooks.upload.domain') ||
                    `http://127.0.0.1:${this.app.getConfig('koa.port')}`;
      const url = `${domain}/upload/images/category_${categoryId}/${fileName}`;

      return this.ok({
        url,
        filename: fileName,
        size: file.data.length,
        categoryId: parseInt(categoryId)
      });

    } catch (error) {
      console.error('图片上传失败:', error);
      throw new CoolCommException(error.message || '图片上传失败');
    }
  }

  /**
   * 记录答题结果
   */
  @Post('/recordAnswer')
  async recordAnswer(@Body() body: { questionId: number; isCorrect: boolean }) {
    const { questionId, isCorrect } = body;
    return this.ok(await this.questionService.recordAnswer(questionId, isCorrect));
  }

  /**
   * 获取题目答题统计
   */
  @Get('/answerStatistics/:id')
  async getAnswerStatistics(@Param('id') id: number) {
    return this.ok(await this.questionService.getAnswerStatistics(id));
  }

  /**
   * 批量获取题目统计
   */
  @Get('/batchStatistics')
  async getBatchStatistics(@Query() query: { provinceId: number; categoryId?: number }) {
    const { provinceId, categoryId } = query;
    return this.ok(await this.questionService.getBatchStatistics(provinceId, categoryId));
  }
}
