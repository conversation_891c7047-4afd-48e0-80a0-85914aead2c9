import { ModuleConfig } from "/@/cool";

export default (): ModuleConfig => {
	return {
		// 注册全局组件
		components: [
			import("./components/question-options.vue"),
			import("./components/import-dialog.vue"),
			import("./components/copy-question.vue"),
			import("./components/basic-config.vue")
		],
		// 路由配置
		views: [
			{
				path: "/question/version",
				meta: {
					label: "版本管理"
				},
				component: () => import("./views/version.vue")
			}
		]
	};
};
