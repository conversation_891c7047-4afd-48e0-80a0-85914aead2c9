<template>
	<div class="version-management">
		<cl-view-group ref="ViewGroup">
			<!-- 左侧地区和分类选择 -->
			<template #left>
				<div class="scope">
					<div class="head">
						<span class="label">地区和分类选择</span>
					</div>

					<!-- 地区选择器 -->
					<div class="region-selector">
						<span class="selector-label">选择地区：</span>
						<el-select
							v-model="selectedRegionId"
							placeholder="请选择地区"
							clearable
							@change="onRegionChange"
							class="selector-input"
						>
							<el-option
								v-for="region in regions"
								:key="region.id"
								:label="region.name"
								:value="region.id"
							/>
						</el-select>
					</div>

					<!-- 分类选择器 -->
					<div class="category-selector" v-if="selectedRegionId">
						<span class="selector-label">选择分类：</span>
						<el-tree-select
							v-model="selectedCategoryId"
							:data="treeCategoryOptions"
							clearable
							filterable
							check-strictly
							default-expand-all
							placeholder="请选择分类"
							:props="{
								label: 'name',
								value: 'id',
								children: 'children'
							}"
							@change="onCategoryChange"
							class="selector-input"
						/>
						<div v-if="selectedCategoryName" class="category-path">
							{{ selectedCategoryName }}
						</div>
					</div>

					<!-- 科目列表 -->
					<div class="subject-list" v-if="selectedCategoryId">
						<div class="search">
							<el-input
								v-model="subjectKeyword"
								placeholder="搜索科目"
								clearable
								@input="filterSubjects"
							/>
						</div>
						<div class="data">
							<el-scrollbar>
								<ul class="list">
									<li v-for="subject in filteredSubjects" :key="subject.id">
										<div
											class="item"
											:class="{ 'is-active': selectedSubject?.id === subject.id }"
											@click="selectSubject(subject)"
										>
											{{ subject.name }}
											<el-tag v-if="subject.questionCount" size="small" type="info">
												{{ subject.questionCount }}题
											</el-tag>
										</div>
									</li>
								</ul>
							</el-scrollbar>
						</div>
					</div>
				</div>
			</template>

			<!-- 右侧版本管理 -->
			<template #right>
				<div v-if="!selectedSubject" class="empty-state">
					<el-empty description="请依次选择地区、分类、科目查看版本信息" />
				</div>

				<div v-else class="version-content">
					<!-- 科目信息 -->
					<div class="subject-info">
						<h3>{{ selectedRegionName }} - {{ selectedCategoryName }} - {{ selectedSubject.name }}</h3>
						<p class="subject-path">版本管理</p>
						<p>{{ selectedSubject.description || '暂无描述' }}</p>
					</div>

					<!-- 版本操作工具栏 -->
					<div class="version-toolbar">
						<el-button type="primary" @click="createNewVersion">
							<el-icon><Plus /></el-icon>
							创建新版本
						</el-button>
						<el-button type="success" @click="refreshVersions">
							<el-icon><Refresh /></el-icon>
							刷新
						</el-button>
					</div>

					<!-- 版本列表 -->
					<div class="version-list">
						<el-table :data="versions" v-loading="loading">
							<el-table-column prop="version" label="版本号" width="120" />
							<el-table-column prop="name" label="版本名称" min-width="200" />
							<el-table-column prop="status" label="状态" width="100">
								<template #default="{ row }">
									<el-tag :type="getStatusType(row.status)">
										{{ getStatusText(row.status) }}
									</el-tag>
								</template>
							</el-table-column>
							<el-table-column prop="questionCount" label="题目数量" width="100" />
							<el-table-column label="标识" width="120">
								<template #default="{ row }">
									<el-tag v-if="row.isActive" type="success" size="small">活跃</el-tag>
									<el-tag v-if="row.isDefault" type="primary" size="small">默认</el-tag>
								</template>
							</el-table-column>
							<el-table-column prop="createTime" label="创建时间" width="180">
								<template #default="{ row }">
									{{ formatTime(row.createTime) }}
								</template>
							</el-table-column>
							<el-table-column label="操作" width="300" fixed="right">
								<template #default="{ row }">
									<el-button-group>
										<el-button
											v-if="row.status === 0"
											size="small"
											@click="updateStatus(row, 1)"
										>
											测试
										</el-button>
										<el-button
											v-if="row.status === 1"
											size="small"
											type="warning"
											@click="publishVersion(row)"
										>
											发布
										</el-button>
										<el-button
											v-if="row.status === 2"
											size="small"
											type="success"
											@click="activateVersion(row)"
										>
											激活
										</el-button>
										<el-button
											v-if="row.status === 3 && !row.isActive"
											size="small"
											type="info"
											@click="rollbackVersion(row)"
										>
											回滚
										</el-button>
										<el-button
											size="small"
											@click="viewQuestions(row)"
										>
											查看题目
										</el-button>
										<el-button
											size="small"
											@click="copyQuestions(row)"
										>
											复制题目
										</el-button>
									</el-button-group>
								</template>
							</el-table-column>
						</el-table>
					</div>
				</div>
			</template>
		</cl-view-group>

		<!-- 创建版本对话框 -->
		<el-dialog v-model="createDialogVisible" title="创建新版本" width="600px">
			<el-form :model="newVersionForm" label-width="100px">
				<el-form-item label="版本名称" required>
					<el-input v-model="newVersionForm.name" placeholder="请输入版本名称" />
				</el-form-item>
				<el-form-item label="版本描述">
					<el-input
						v-model="newVersionForm.description"
						type="textarea"
						rows="3"
						placeholder="请输入版本描述"
					/>
				</el-form-item>
			</el-form>
			<template #footer>
				<el-button @click="createDialogVisible = false">取消</el-button>
				<el-button type="primary" @click="confirmCreateVersion">确定</el-button>
			</template>
		</el-dialog>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Refresh } from '@element-plus/icons-vue';
import { useCool } from '/@/cool';
import { useViewGroup } from '/@/plugins/view';

const { service } = useCool();

// 视图组
const { ViewGroup } = useViewGroup({
	title: '版本管理',
	custom: true
});

// 数据
const regions = ref<any[]>([]);
const selectedRegionId = ref<number | null>(null);
const selectedRegionName = ref('');

const categoryOptions = ref<any[]>([]);
const treeCategoryOptions = ref<any[]>([]);
const selectedCategoryId = ref<number | null>(null);
const selectedCategoryName = ref('');

const subjects = ref<any[]>([]);
const filteredSubjects = ref<any[]>([]);
const subjectKeyword = ref('');
const selectedSubject = ref<any | null>(null);

const versions = ref<any[]>([]);
const loading = ref(false);

// 创建版本对话框
const createDialogVisible = ref(false);
const newVersionForm = ref({
	name: '',
	description: ''
});

// 计算属性
const statusOptions = {
	0: { text: '开发中', type: 'info' },
	1: { text: '测试中', type: 'warning' },
	2: { text: '预发布', type: 'primary' },
	3: { text: '生产中', type: 'success' },
	4: { text: '已废弃', type: 'danger' }
};

// 方法
function getStatusText(status: number) {
	return statusOptions[status]?.text || '未知';
}

function getStatusType(status: number) {
	return statusOptions[status]?.type || 'info';
}

function formatTime(time: string) {
	return new Date(time).toLocaleString();
}

// 加载地区列表
async function loadRegions() {
	try {
		const res = await service.region.region.list();
		regions.value = res.data || res;
	} catch (error) {
		console.error('加载地区列表失败:', error);
		ElMessage.error('加载地区列表失败');
	}
}

// 地区选择变化
function onRegionChange(regionId: number | null) {
	selectedRegionId.value = regionId;
	if (regionId) {
		const region = regions.value.find(r => r.id === regionId);
		selectedRegionName.value = region?.name || '';
		loadCategories();
	} else {
		selectedRegionName.value = '';
		categoryOptions.value = [];
		treeCategoryOptions.value = [];
		selectedCategoryId.value = null;
		selectedCategoryName.value = '';
		subjects.value = [];
		filteredSubjects.value = [];
		selectedSubject.value = null;
		versions.value = [];
	}
}

// 加载分类列表
async function loadCategories() {
	if (!selectedRegionId.value) return;

	try {
		const res = await service.category.category.list({
			provinceId: selectedRegionId.value
		});
		categoryOptions.value = res.data || res;
		treeCategoryOptions.value = buildCategoryTree(categoryOptions.value);
	} catch (error) {
		console.error('加载分类列表失败:', error);
		ElMessage.error('加载分类列表失败');
	}
}

// 构建分类树
function buildCategoryTree(categories: any[]) {
	const tree = [];
	const map = {};

	// 创建映射
	categories.forEach(item => {
		map[item.id] = { ...item, children: [] };
	});

	// 构建树结构
	categories.forEach(item => {
		if (item.parentId === 0 || !map[item.parentId]) {
			tree.push(map[item.id]);
		} else {
			map[item.parentId].children.push(map[item.id]);
		}
	});

	return tree;
}

// 分类选择变化
function onCategoryChange(categoryId: number | null) {
	selectedCategoryId.value = categoryId;
	if (categoryId) {
		selectedCategoryName.value = getCategoryName(categoryId);
		loadSubjects();
	} else {
		selectedCategoryName.value = '';
		subjects.value = [];
		filteredSubjects.value = [];
		selectedSubject.value = null;
		versions.value = [];
	}
}

// 获取分类名称
function getCategoryName(categoryId: number): string {
	const category = categoryOptions.value.find(c => c.id === categoryId);
	return category?.name || '';
}

// 加载科目列表
async function loadSubjects() {
	if (!selectedRegionId.value || !selectedCategoryId.value) return;

	try {
		const res = await service.subject.subject.list({
			provinceId: selectedRegionId.value,
			categoryId: selectedCategoryId.value
		});
		subjects.value = res.data || res;
		filteredSubjects.value = subjects.value;

		// 自动选择第一个科目
		if (subjects.value.length > 0) {
			selectSubject(subjects.value[0]);
		}
	} catch (error) {
		console.error('加载科目列表失败:', error);
		ElMessage.error('加载科目列表失败');
	}
}

// 筛选科目
function filterSubjects() {
	if (!subjectKeyword.value) {
		filteredSubjects.value = subjects.value;
	} else {
		filteredSubjects.value = subjects.value.filter(subject =>
			subject.name.includes(subjectKeyword.value)
		);
	}
}

// 选择科目
function selectSubject(subject: any) {
	selectedSubject.value = subject;
	loadVersions();
}

// 加载版本列表
async function loadVersions() {
	if (!selectedSubject.value) return;

	loading.value = true;
	try {
		const res = await service.question.version.request({
			url: `/list/${selectedSubject.value.id}`,
			method: 'GET'
		});
		versions.value = res.data || res;
	} catch (error) {
		console.error('加载版本列表失败:', error);
		ElMessage.error('加载版本列表失败');
	} finally {
		loading.value = false;
	}
}

// 刷新版本列表
function refreshVersions() {
	loadVersions();
}

// 创建新版本
function createNewVersion() {
	newVersionForm.value = { name: '', description: '' };
	createDialogVisible.value = true;
}

// 确认创建版本
async function confirmCreateVersion() {
	if (!newVersionForm.value.name) {
		ElMessage.error('请输入版本名称');
		return;
	}

	try {
		await service.question.version.request({
			url: '/create',
			method: 'POST',
			data: {
				...newVersionForm.value,
				subjectId: selectedSubject.value.id,
				provinceId: selectedSubject.value.provinceId,
				categoryId: selectedSubject.value.categoryId
			}
		});

		ElMessage.success('版本创建成功');
		createDialogVisible.value = false;
		loadVersions();
	} catch (error) {
		console.error('创建版本失败:', error);
		ElMessage.error('创建版本失败');
	}
}

// 更新版本状态
async function updateStatus(version: any, status: number) {
	try {
		await service.question.version.request({
			url: `/updateStatus/${version.id}`,
			method: 'POST',
			data: { status }
		});

		ElMessage.success('状态更新成功');
		loadVersions();
	} catch (error) {
		console.error('更新状态失败:', error);
		ElMessage.error('更新状态失败');
	}
}

// 发布版本
async function publishVersion(version: any) {
	try {
		await ElMessageBox.confirm('确定要发布此版本吗？', '确认发布', {
			type: 'warning'
		});

		await service.question.version.request({
			url: `/publish/${version.id}`,
			method: 'POST'
		});

		ElMessage.success('版本发布成功');
		loadVersions();
	} catch (error) {
		if (error !== 'cancel') {
			console.error('发布版本失败:', error);
			ElMessage.error('发布版本失败');
		}
	}
}

// 激活版本
async function activateVersion(version: any) {
	try {
		await ElMessageBox.confirm(
			'激活此版本将会使其成为新用户的默认版本，确定要激活吗？',
			'确认激活',
			{ type: 'warning' }
		);

		await service.question.version.request({
			url: `/activate/${version.id}`,
			method: 'POST',
			data: {}
		});

		ElMessage.success('版本激活成功');
		loadVersions();
	} catch (error) {
		if (error !== 'cancel') {
			console.error('激活版本失败:', error);
			ElMessage.error('激活版本失败');
		}
	}
}

// 回滚版本
async function rollbackVersion(version: any) {
	try {
		await ElMessageBox.confirm(
			'确定要回滚到此版本吗？这将停用当前活跃版本。',
			'确认回滚',
			{ type: 'warning' }
		);

		const currentVersion = versions.value.find(v => v.isActive);
		if (!currentVersion) {
			ElMessage.error('未找到当前活跃版本');
			return;
		}

		await service.question.version.request({
			url: '/rollback',
			method: 'POST',
			data: {
				currentVersionId: currentVersion.id,
				targetVersionId: version.id
			}
		});

		ElMessage.success('版本回滚成功');
		loadVersions();
	} catch (error) {
		if (error !== 'cancel') {
			console.error('回滚版本失败:', error);
			ElMessage.error('回滚版本失败');
		}
	}
}

// 查看题目
function viewQuestions(version: any) {
	// 跳转到题目管理页面，并传递版本参数
	// 这里需要根据您的路由配置来实现
	ElMessage.info('跳转到题目管理页面');
}

// 复制题目
function copyQuestions(version: any) {
	// 实现题目复制功能
	ElMessage.info('题目复制功能');
}

// 组件挂载
onMounted(() => {
	loadRegions();
});
</script>

<style lang="scss" scoped>
.version-management {
	height: 100%;
}

.scope {
	display: flex;
	flex-direction: column;
	height: 100%;

	.head {
		padding: 16px;
		border-bottom: 1px solid var(--el-border-color-extra-light);
		font-weight: 500;
	}

	.region-selector,
	.category-selector {
		padding: 16px;
		border-bottom: 1px solid var(--el-border-color-extra-light);

		.selector-label {
			display: block;
			margin-bottom: 8px;
			font-weight: 500;
			color: var(--el-text-color-primary);
		}

		.selector-input {
			width: 100%;
		}

		.category-path {
			margin-top: 8px;
			padding: 8px;
			background: var(--el-fill-color-light);
			border-radius: 4px;
			font-size: 12px;
			color: var(--el-text-color-regular);
		}
	}

	.subject-list {
		flex: 1;
		display: flex;
		flex-direction: column;

		.search {
			padding: 16px;
		}
	}

	.data {
		flex: 1;
		overflow: hidden;
		padding: 0 16px;

		.list {
			.item {
				padding: 12px 16px;
				margin-bottom: 8px;
				border-radius: 6px;
				cursor: pointer;
				transition: all 0.2s;
				display: flex;
				justify-content: space-between;
				align-items: center;

				&:hover {
					background-color: var(--el-fill-color-light);
				}

				&.is-active {
					background-color: var(--el-color-primary-light-9);
					color: var(--el-color-primary);
				}
			}
		}
	}
}

.empty-state {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100%;
}

.version-content {
	padding: 20px;

	.subject-info {
		margin-bottom: 20px;
		padding: 16px;
		background: var(--el-bg-color-page);
		border-radius: 6px;

		h3 {
			margin: 0 0 8px 0;
			color: var(--el-text-color-primary);
			font-size: 18px;
		}

		.subject-path {
			margin: 0 0 8px 0;
			color: var(--el-color-primary);
			font-weight: 500;
			font-size: 14px;
		}

		p {
			margin: 0;
			color: var(--el-text-color-regular);
		}
	}

	.version-toolbar {
		margin-bottom: 20px;
		display: flex;
		gap: 12px;
	}

	.version-list {
		.el-button-group {
			.el-button {
				margin-left: 0;
			}
		}
	}
}
</style>
