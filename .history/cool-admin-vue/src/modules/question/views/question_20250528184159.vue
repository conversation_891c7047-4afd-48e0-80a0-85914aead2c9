<template>
	<div>
		<cl-view-group ref="ViewGroup">
			<!-- 左侧地区列表 -->
			<template #left>
				<div class="scope">
					<div class="head">
						<el-text class="label">{{ t('地区列表') }}</el-text>

						<el-tooltip :content="t('刷新')">
							<div class="icon" @click="loadRegions()">
								<cl-svg name="refresh" />
							</div>
						</el-tooltip>
					</div>

					<div v-if="true" class="search">
						<el-input v-model="regionKeyWord" :placeholder="t('搜索地区名称')" clearable @change="loadRegions()">
							<template #prefix>
								<cl-svg name="search" :size="16" />
							</template>
						</el-input>
					</div>

					<div class="data">
						<el-scrollbar>
							<ul class="list">
								<li v-for="(item, index) in regionList" :key="index" @click="selectRegion(item)">
									<div class="item" :class="{ 'is-active': selectedRegion?.id == item.id }">
										<span class="text-ellipsis overflow-hidden mr-2">
											{{ item.name }}
										</span>

										<cl-svg name="right" class="ml-auto" v-show="selectedRegion?.id == item.id" />
										<el-tag v-if="item.type === 1" size="small" type="warning"
											class="ml-2">直辖市</el-tag>
									</div>
								</li>

								<el-empty v-if="regionList.length === 0" :image-size="80" />
							</ul>
						</el-scrollbar>
					</div>
				</div>
			</template>

			<!-- 右侧题目管理 -->
			<template #title>
				<span class="title">
					{{ selectedRegion?.name ? `${selectedRegion.name} - 题目管理` : t('请选择地区') }}
				</span>
			</template>

			<template #right>
				<!-- 分类选择器 - 保持原有样式 -->
				<div class="category-selector" v-if="selectedRegion">
					<span class="selector-label">选择分类：</span>
					<el-tree-select v-model="selectedCategoryId" :data="treeCategoryOptions" clearable filterable
						check-strictly default-expand-all placeholder="请选择分类" :props="{
							label: 'name',
							value: 'id',
							children: 'children'
						}" @change="onCategoryChange" class="selector-input" />
					<span v-if="selectedCategoryName" class="category-path">
						{{ selectedCategoryName }}
					</span>
					<span v-else class="category-placeholder">
						未选择分类
					</span>
				</div>

				<!-- CRUD容器 - 完全参考用户页面的标准布局 -->
				<cl-crud ref="Crud">
					<cl-row>
						<!-- 刷新按钮 -->
						<cl-refresh-btn />
						<!-- 新增题目按钮 -->
						<cl-add-btn @click="addQuestion" :disabled="!selectedRegion || !selectedCategoryId" />
						<!-- 批量导入按钮 -->
						<el-button type="success" :disabled="!selectedRegion || !selectedCategoryId"
							@click="openImportDialog">
							批量导入
						</el-button>
						<!-- 批量导出按钮 -->
						<el-button type="warning" :disabled="!selectedRegion || !selectedCategoryId"
							@click="exportQuestions">
							批量导出
						</el-button>
						<!-- 复制题目按钮 -->
						<el-button type="info" :disabled="!selectedRegion || !selectedCategoryId"
							@click="openCopyDialog">
							复制题目
						</el-button>
						<!-- 删除按钮 -->
						<cl-multi-delete-btn />
						<cl-flex1 />
						<!-- 精简搜索 -->
						<cl-search ref="Search" />
					</cl-row>

					<cl-row>
						<!-- 数据表格 -->
						<cl-table ref="Table" />
					</cl-row>

					<cl-row>
						<cl-flex1 />
						<cl-pagination />
					</cl-row>

					<!-- 新增、编辑 -->
					<cl-upsert ref="Upsert" />
				</cl-crud>
			</template>
		</cl-view-group>

		<!-- 导入对话框 -->
		<import-dialog v-model="importDialogVisible" :province-id="selectedRegion?.id" :category-id="selectedCategoryId"
			:province-name="selectedRegion?.name" :category-name="selectedCategoryName" @success="onImportSuccess" />

		<!-- 复制题目对话框 -->
		<copy-question v-model="copyDialogVisible" :source-province-id="selectedRegion?.id"
			:source-category-id="selectedCategoryId" @success="onCopySuccess" />
	</div>
</template>

<script lang="ts" setup>





import { useCrud, useTable, useUpsert, useSearch } from '@cool-vue/crud';
import { useCool } from '/@/cool';
import { useI18n } from 'vue-i18n';
import { reactive, ref, onMounted, watch, nextTick, computed } from 'vue';
import { useViewGroup } from '/@/plugins/view';
import { ElMessage } from 'element-plus';


// 组件已通过模块配置全局注册，无需导入

// 组件已通过模块配置全局注册

const { service } = useCool();


const { t } = useI18n();

// 视图组
const { ViewGroup } = useViewGroup({
	title: t('题目管理'),
	custom: true
});

// 地区实体类型定义
interface RegionEntity {
	id: number;
	name: string;
	type: number;
	parentId: number | null;
	code: string;
	orderNum: number;
	isHot: number;
}

// 地区列表
const regionList = ref<RegionEntity[]>([]);
// 选中的地区
const selectedRegion = ref<RegionEntity | null>(null);
// 地区搜索关键字
const regionKeyWord = ref('');

// 分类相关
const categoryOptions = ref<any[]>([]);
const selectedCategoryId = ref<number | undefined>(undefined);
const selectedCategoryName = ref<string>('');

// 为tree-select创建静态数据，避免响应式冲突
const treeCategoryOptions = ref<any[]>([]);

// 科目相关
const subjectOptions = ref<any[]>([]);

// 对话框状态
const importDialogVisible = ref(false);
const copyDialogVisible = ref(false);

// 定义字典数据 - 使用后端实体字典配置
const dictOptions = reactive({
	type: [
		{ label: "单选题", value: 0, type: "primary" },
		{ label: "多选题", value: 1, type: "success" },
		{ label: "判断题", value: 2, type: "warning" },
	],
	difficulty: [
		{ label: "简单", value: 0, type: "success" },
		{ label: "中等", value: 1, type: "warning" },
		{ label: "困难", value: 2, type: "danger" },
	],
	status: [
		{ label: "禁用", value: 0, type: "danger" },
		{ label: "启用", value: 1, type: "success" },
	],
});

// 获取地区名称
function getRegionName(provinceId: number): string {
	const region = regionList.value.find(r => r.id === provinceId);
	return region ? region.name : '';
}

// 获取分类名称（包含完整路径）- 用于表单显示
function getCategoryName(categoryId: number): string {
	const findCategoryPath = (categories: any[], id: number, path: string[] = []): string[] | null => {
		for (const category of categories) {
			const currentPath = [...path, category.name];
			if (category.id === id) {
				return currentPath;
			}
			if (category.children) {
				const found = findCategoryPath(category.children, id, currentPath);
				if (found) return found;
			}
		}
		return null;
	};

	const categoryPath = findCategoryPath(categoryOptions.value, categoryId);
	return categoryPath ? categoryPath.join(' > ') : '';
}

// 存储上次选择的分类路径，实现跨地区分类映射
const lastSelectedCategoryPath = ref<string[]>([]);

// 根据分类路径查找对应的分类ID
function findCategoryByPath(categories: any[], path: string[]): number | null {
	if (path.length === 0) return null;

	for (const category of categories) {
		if (category.name === path[0]) {
			if (path.length === 1) {
				// 找到目标分类
				return category.id;
			} else {
				// 继续在子分类中查找
				if (category.children) {
					const result = findCategoryByPath(category.children, path.slice(1));
					if (result) return result;
				}
			}
		}
	}
	return null;
}

// 根据分类ID获取分类路径数组
function getCategoryPath(categories: any[], categoryId: number): string[] | null {
	const findPath = (cats: any[], id: number, path: string[] = []): string[] | null => {
		for (const category of cats) {
			const currentPath = [...path, category.name];
			if (category.id === id) {
				return currentPath;
			}
			if (category.children) {
				const found = findPath(category.children, id, currentPath);
				if (found) return found;
			}
		}
		return null;
	};

	return findPath(categories, categoryId);
}

// 加载地区列表
async function loadRegions() {
	try {
		const res = await service.region.region.list({
			keyWord: regionKeyWord.value
		});

		const actualData = Array.isArray(res) ? res : ((res as any)?.data || []);
		regionList.value = actualData.filter((item: RegionEntity) => item.type === 0 || item.type === 1);
	} catch (error) {
		console.error('加载地区列表失败:', error);
		ElMessage.error(t('加载地区列表失败'));
	}
}

// 选择地区 - 实现跨地区分类映射
function selectRegion(region: RegionEntity) {
	selectedRegion.value = region;

	// 保存当前选择的分类路径（用于跨地区映射）
	if (selectedCategoryId.value && categoryOptions.value.length > 0) {
		const currentPath = getCategoryPath(categoryOptions.value, selectedCategoryId.value);
		if (currentPath) {
			lastSelectedCategoryPath.value = currentPath;
		}
	}

	// 暂时清空分类选择，等待新地区的分类数据加载
	selectedCategoryId.value = undefined;
	selectedCategoryName.value = '';

	// 清空分类数据
	categoryOptions.value = [];
	treeCategoryOptions.value = [];

	// 加载该地区的分类选项
	loadCategoryOptions();

	// 清空题目数据
	if (Crud.value) {
		Crud.value.refresh();
	}
}

// 加载分类选项 - 支持跨地区分类路径映射
async function loadCategoryOptions() {
	if (!selectedRegion.value) return;

	try {
		const res = await service.category.category.request({
			url: `/tree/${selectedRegion.value.id}`,
			method: 'GET'
		});

		const actualData = Array.isArray(res) ? res : ((res as any)?.data || []);
		categoryOptions.value = actualData;
		// 同时更新tree-select的静态数据，避免响应式冲突
		treeCategoryOptions.value = JSON.parse(JSON.stringify(actualData));

		// 尝试根据分类路径恢复选择（跨地区映射）
		if (lastSelectedCategoryPath.value.length > 0) {
			const mappedCategoryId = findCategoryByPath(actualData, lastSelectedCategoryPath.value);
			if (mappedCategoryId) {
				selectedCategoryId.value = mappedCategoryId;
				selectedCategoryName.value = getCategoryName(mappedCategoryId);

				// 加载科目选项
				loadSubjectOptions();

				// 刷新题目数据
				if (Crud.value) {
					Crud.value.refresh();
				}
			}
		}
	} catch (error) {
		console.error('加载分类选项失败:', error);
		ElMessage.error(t('加载分类选项失败'));
	}
}

// 分类选择变化 - 手动切换分类时更新路径记录
function onCategoryChange(categoryId: number | undefined) {
	if (categoryId) {
		selectedCategoryName.value = getCategoryName(categoryId);

		// 更新分类路径记录（用于跨地区映射）
		const currentPath = getCategoryPath(categoryOptions.value, categoryId);
		if (currentPath) {
			lastSelectedCategoryPath.value = currentPath;
		}

		// 加载该分类下的科目选项
		loadSubjectOptions();

		// 自动刷新题目数据
		if (Crud.value) {
			Crud.value.refresh();
		}
	} else {
		selectedCategoryName.value = '';
		subjectOptions.value = [];
		// 清空分类时也清空路径记录
		lastSelectedCategoryPath.value = [];
	}
}

// 加载科目选项
async function loadSubjectOptions() {
	if (!selectedRegion.value || !selectedCategoryId.value) return;

	try {
		const res = await service.subject.subject.list({
			provinceId: selectedRegion.value.id,
			categoryId: selectedCategoryId.value
		});

		const actualData = Array.isArray(res) ? res : ((res as any)?.data || []);
		subjectOptions.value = actualData.map((item: any) => ({
			label: item.name,
			value: item.id
		}));
	} catch (error) {
		console.error('加载科目选项失败:', error);
		ElMessage.error(t('加载科目选项失败'));
	}
}

// 新增题目 - 符合Cool Admin Vue标准
async function addQuestion() {
	if (!selectedRegion.value) {
		ElMessage.warning(t('请先选择地区'));
		return;
	}

	if (!selectedCategoryId.value) {
		ElMessage.warning(t('请先选择分类'));
		return;
	}

	// 根据地区和分类查找对应的科目
	const subjectId = await findSubjectByRegionAndCategory(selectedRegion.value.id, selectedCategoryId.value);

	if (!subjectId) {
		ElMessage.warning(t('未找到对应的科目，请先创建科目'));
		return;
	}

	// 使用标准的rowAppend方法，预填充必要数据
	Crud.value?.rowAppend({
		provinceId: selectedRegion.value.id,
		categoryId: selectedCategoryId.value,
		subjectId: subjectId,
		type: 0,
		difficulty: 0,
		score: 1.0,
		status: 1,
		orderNum: 0
	});
}

// 根据地区和分类查找对应的科目ID
async function findSubjectByRegionAndCategory(provinceId: number, categoryId: number): Promise<number | null> {
	try {
		const res = await service.subject.subject.list({
			provinceId: provinceId,
			categoryId: categoryId
		});

		const actualData = Array.isArray(res) ? res : ((res as any)?.data || []);

		if (actualData.length > 0) {
			// 返回第一个匹配的科目ID（因为每个地区+分类组合应该只有一个科目）
			return actualData[0].id;
		}

		return null;
	} catch (error) {
		return null;
	}
}

// 打开导入对话框
function openImportDialog() {
	if (!selectedRegion.value || !selectedCategoryId.value) {
		ElMessage.warning(t('请先选择地区和分类'));
		return;
	}

	importDialogVisible.value = true;
}

// 导入成功回调
function onImportSuccess() {
	ElMessage.success(t('导入成功'));
	if (Crud.value) {
		Crud.value.refresh();
	}
}

// 导出题目
async function exportQuestions() {
	if (!selectedRegion.value || !selectedCategoryId.value) {
		ElMessage.warning(t('请先选择地区和分类'));
		return;
	}

	try {
		const res = await service.question.question.request({
			url: '/export',
			method: 'POST',
			data: {
				provinceId: selectedRegion.value.id,
				categoryId: selectedCategoryId.value
			}
		});

		// 获取导出数据
		const exportData = res.data || res;

		if (!exportData || exportData.length === 0) {
			ElMessage.warning(t('没有可导出的数据'));
			return;
		}

		// 使用 XLSX 库生成Excel文件
		const XLSX = await import('xlsx');
		const worksheet = XLSX.utils.json_to_sheet(exportData);
		const workbook = XLSX.utils.book_new();
		XLSX.utils.book_append_sheet(workbook, worksheet, '题目数据');

		// 下载文件
		const fileName = `题目_${selectedRegion.value.name}_${selectedCategoryName.value}.xlsx`;
		XLSX.writeFile(workbook, fileName);

		ElMessage.success(t('导出成功'));
	} catch (error) {
		ElMessage.error(t('导出失败'));
	}
}

// 打开复制对话框
function openCopyDialog() {
	if (!selectedRegion.value || !selectedCategoryId.value) {
		ElMessage.warning(t('请先选择地区和分类'));
		return;
	}
	copyDialogVisible.value = true;
}

// 复制成功回调
function onCopySuccess() {
	ElMessage.success(t('复制成功'));
}

// cl-upsert - 符合Cool Admin Vue标准配置
const Upsert = useUpsert({
	items: [
		// 添加分类路径显示
		{
			label: t("分类路径"),
			prop: "categoryPath",
			component: {
				name: "el-input",
				props: {
					readonly: true,
					placeholder: "自动显示分类路径",
					style: "background-color: var(--el-fill-color-light); color: var(--el-color-primary);"
				}
			},
			span: 24,
		},
		{
			label: t("题目类型"),
			prop: "type",
			component: { name: "el-radio-group", options: dictOptions.type },
			required: true,
			span: 8,
		},
		// 与题目类型同行：难度、状态（单选框）
		{
			label: t("难度"),
			prop: "difficulty",
			component: { name: "el-radio-group", options: dictOptions.difficulty },
			value: 0,
			required: true,
			span: 8,
		},
		{
			label: t("状态"),
			prop: "status",
			component: { name: "el-radio-group", options: dictOptions.status },
			value: 1,
			required: true,
			span: 8,
		},
		{
			label: t("题目内容"),
			prop: "content",
			component: {
				name: "el-input",
				props: {
					type: "textarea",
					rows: 3,
					clearable: true,
					placeholder: "请输入题目内容"
				}
			},
			required: true,
			span: 18,
		},
		{
			label: t("题目图片"),
			prop: "image",
			component: {
				name: "cl-upload",
				props: {
					type: "image",
					accept: "image/*",
					limit: 1,
					limitSize: 2,
					size: [120, 120],
					text: "选择图片",
					showFileList: true,
					multiple: false
				}
			},
			span: 6,
		},
		{
			label: t("正确答案"),
			prop: "correctAnswer",
			component: {
				name: "el-input",
				props: {
					clearable: true,
					placeholder: "请输入正确答案，如：A 或 A,C"
				}
			},
			required: true,
			span: 12,
		},
		// 分数、排序单独一行
		{
			label: t("分数"),
			prop: "score",
			component: { name: "el-input-number", props: { min: 0.1, max: 100, step: 0.1, precision: 1, size: "default" } },
			value: 1.0,
			required: true,
			span: 12,
		},
		{
			label: t("排序"),
			prop: "orderNum",
			component: { name: "el-input-number", props: { min: 0, size: "default" } },
			value: 0,
			span: 12,
		},

		{
			label: t("题目选项"),
			prop: "options",
			component: {
				name: "question-options",
				props: {
					questionType: 0,
					categoryId: selectedCategoryId
				}
			},
			span: 24,
		},
		{
			label: t("题目解析"),
			prop: "analysis",
			component: { name: "el-input", props: { type: "textarea", rows: 2, clearable: true, placeholder: "请输入题目解析（可选）" } },
			span: 24,
		},
	],
	// 弹窗打开的事件，这个时候还未有表单数据
	onOpen() {
		// 表单打开时的初始化逻辑
	},
	// 弹窗打开后，已经得到了表单数据
	onOpened(data: any) {
		// 设置分类路径显示
		if (selectedCategoryName.value) {
			data.categoryPath = selectedCategoryName.value;
		}

		// 设置题目类型到选项组件
		nextTick(() => {
			const optionsField = Upsert.value?.items?.find((item: any) => item.prop === 'options');
			if (optionsField && optionsField.component && optionsField.component.props && data.type !== undefined) {
				optionsField.component.props.questionType = data.type;
				console.log('设置选项组件题目类型:', data.type);
			}
		});
	},
	// 编辑时获取完整数据（包含选项）- 使用框架标准接口
	async onInfo(data: any, { done }) {
		try {
			console.log('=== 开始获取题目详情 ===');
			console.log('题目ID:', data.id);

			// 使用标准的info接口
			const result = await service.question.question.info(data.id);

			console.log('后端返回的完整数据:', result);
			console.log('原始选项数据:', result.options);
			console.log('题目类型:', result.type);

			// 如果选项是字符串，需要解析为数组
			if (result.options && typeof result.options === 'string') {
				try {
					result.options = JSON.parse(result.options);
					console.log('解析后的选项数据:', result.options);
				} catch (error) {
					console.error('解析选项JSON失败:', error);
					result.options = [];
				}
			}

			// 设置分类路径显示
			if (selectedCategoryName.value) {
				result.categoryPath = selectedCategoryName.value;
			}

			console.log('准备传递给表单的数据:', result);
			// 直接返回结果，框架会自动处理
			done(result);
		} catch (error) {
			console.error('获取题目详情失败:', error);
			// 如果获取失败，使用传入的数据
			if (selectedCategoryName.value) {
				data.categoryPath = selectedCategoryName.value;
			}
			done(data);
		}
	},
	// 添加提交钩子，处理选项数据为JSON格式
	async onSubmit(data: any, { next }) {
		// 移除不需要提交的字段
		const submitData = { ...data };
		delete submitData.categoryPath; // 移除显示用的分类路径字段

		// 如果有选项数据，确保是JSON字符串格式
		if (submitData.options && Array.isArray(submitData.options)) {
			submitData.options = JSON.stringify(submitData.options);
		}

		// 使用标准的CRUD接口
		await next(submitData);
	}
});

// 监听题目类型变化，重置选项数据 - 符合Cool Admin Vue规范
watch(() => Upsert.value?.form?.type, (newType: number, oldType: number) => {
	if (newType !== undefined && newType !== oldType && Upsert.value?.form) {
		// 重置选项数据
		Upsert.value.form.options = [];

		// 动态更新组件props中的questionType
		nextTick(() => {
			const optionsField = Upsert.value?.items?.find((item: any) => item.prop === 'options');
			if (optionsField && optionsField.component && optionsField.component.props) {
				optionsField.component.props.questionType = newType;
			}
		});
	}
});

// cl-table - 禁用自动高度，让CSS控制布局
const Table = useTable({
	autoHeight: false, // 在cl-view-group布局中必须禁用自动高度
	contextMenu: ['refresh'], // 添加右键菜单
	columns: [
		{ type: "selection" },
		{
			label: t("题目类型"),
			prop: "type",
			minWidth: 100,
			dict: dictOptions.type,
		},
		{
			label: t("题目内容"),
			prop: "content",
			minWidth: 300,
			showOverflowTooltip: true,
			// 显示纯文本，不渲染HTML
		},
		{
			label: t("题目图片"),
			prop: "image",
			minWidth: 120,
			component: {
				name: "cl-image",
				props: {
					size: 40,
					preview: true,
					lazy: true
				}
			}
		},
		{
			label: t("正确答案"),
			prop: "correctAnswer",
			minWidth: 100,
			showOverflowTooltip: true,
		},
		{
			label: t("难度级别"),
			prop: "difficulty",
			minWidth: 100,
			dict: dictOptions.difficulty,
		},
		{
			label: t("题目分数"),
			prop: "score",
			minWidth: 80,
			sortable: true,
		},
		{
			label: t("答题次数"),
			prop: "answerCount",
			minWidth: 90,
			sortable: true,
		},
		{
			label: t("正确率"),
			prop: "correctRate",
			minWidth: 90,
			sortable: true,
			formatter: (row: any) => {
				if (row.answerCount === 0) return '-';
				return `${row.correctRate.toFixed(1)}%`;
			},
		},
		{
			label: t("状态"),
			prop: "status",
			minWidth: 80,
			dict: dictOptions.status,
		},
		{
			label: t("排序号"),
			prop: "orderNum",
			minWidth: 80,
			sortable: true,
		},
		{
			label: t("地区"),
			prop: "provinceId",
			minWidth: 100,
			formatter: (row: any) => getRegionName(row.provinceId),
		},
		{
			label: t("科目"),
			prop: "subjectId",
			minWidth: 120,
			formatter: (row: any) => {
				// 从科目选项中查找对应的科目名称
				const subject = subjectOptions.value.find(s => s.value === row.subjectId);
				return subject ? subject.label : `科目ID: ${row.subjectId}`;
			},
		},
		{
			label: t("创建时间"),
			prop: "createTime",
			minWidth: 170,
			sortable: "desc",
			component: { name: "cl-date-text" },
		},
		{ type: "op", buttons: ["edit", "delete"] },
	],
});

// cl-search - 精简搜索配置，使用后端字典
const Search = useSearch({
	items: [
		{
			label: t("关键字"),
			prop: "keyWord",
			component: {
				name: "el-input",
				props: {
					placeholder: "搜索题目内容",
					clearable: true
				}
			}
		},
		{
			label: t("题型"),
			prop: "type",
			component: {
				name: "el-select",
				props: {
					placeholder: "选择题型",
					clearable: true
				},
				options: dictOptions.type.map(item => ({ label: item.label, value: item.value }))
			}
		},
		{
			label: t("难度"),
			prop: "difficulty",
			component: {
				name: "el-select",
				props: {
					placeholder: "选择难度",
					clearable: true
				},
				options: dictOptions.difficulty.map(item => ({ label: item.label, value: item.value }))
			}
		}
	]
});

// cl-crud - 符合Cool Admin Vue标准规范
const Crud = useCrud(
	{
		service: service.question.question,
		// 使用标准的onRefresh钩子，添加必要的筛选条件
		async onRefresh(params: any, { next, render }) {
			// 必须选择地区和分类才能加载数据
			if (!selectedRegion.value || !selectedCategoryId.value) {
				// 渲染空数据，使用正确的分页格式
				render([], {
					page: 1,
					size: 20,
					total: 0
				});
				return;
			}

			// 添加地区和分类筛选条件
			params.provinceId = selectedRegion.value.id;
			params.categoryId = selectedCategoryId.value;

			// 调用标准的page接口
			await next(params);
		}
	},
	() => {
		// CRUD组件初始化完成，不自动刷新
	}
);

// 组件挂载
onMounted(() => {
	loadRegions();
});
</script>

<style lang="scss" scoped>
// 分类选择器 - 参考用户页面的简洁设计
.category-selector {
	display: flex;
	align-items: center;
	gap: 16px;
	padding: 16px 20px;
	border-bottom: 1px solid var(--el-border-color-extra-light);
	background-color: var(--el-bg-color);

	.selector-label {
		font-weight: 500;
		color: var(--el-text-color-regular);
		font-size: 14px;
		white-space: nowrap;
		flex-shrink: 0;
		min-width: 80px;
	}

	.selector-input {
		flex: 1;
		max-width: 300px;
		min-width: 200px;
	}

	.category-path {
		font-size: 13px;
		color: var(--el-color-primary);
		background: var(--el-color-primary-light-9);
		border: 1px solid var(--el-color-primary-light-7);
		padding: 4px 8px;
		border-radius: 4px;
		font-weight: 500;
	}

	.category-placeholder {
		font-size: 13px;
		color: var(--el-text-color-placeholder);
		background: var(--el-fill-color-light);
		border: 1px solid var(--el-border-color-lighter);
		padding: 4px 8px;
		border-radius: 4px;
	}
}

// 左侧地区列表 - 完全参考用户页面的部门列表样式
.scope {
	display: flex;
	flex-direction: column;
	height: 100%;
	width: 100%;
	box-sizing: border-box;
	white-space: nowrap;

	.head {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 40px;
		padding: 0 10px;
		border-bottom: 1px solid var(--el-border-color-extra-light);
		font-size: 14px;

		.label {
			flex: 1;
		}

		.icon {
			display: flex;
			justify-content: center;
			align-items: center;
			list-style: none;
			margin-left: 5px;
			cursor: pointer;
			border-radius: 6px;
			font-size: 16px;
			height: 26px;
			width: 26px;
			color: var(--el-text-color-primary);

			.cl-svg {
				outline: none;
			}

			&:hover {
				background-color: var(--el-fill-color-light);
			}
		}
	}

	.search {
		padding: 10px;
	}

	.data {
		flex: 1;
		overflow: hidden;
		box-sizing: border-box;
		padding: 10px;

		.el-scrollbar {
			height: 100%;
		}

		.list {
			height: 100%;

			li {
				margin-bottom: 8px;

				&:last-child {
					margin-bottom: 0;
				}

				.item {
					display: flex;
					align-items: center;
					list-style: none;
					box-sizing: border-box;
					padding: 12px 16px;
					cursor: pointer;
					font-size: 14px;
					border-radius: 4px;
					color: var(--el-text-color-regular);
					position: relative;
					background-color: transparent;
					border: 1px solid transparent;
					transition: all 0.2s;
					height: 38px; // 与用户页面的部门节点高度一致
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;

					&:hover {
						background-color: var(--el-fill-color-light);
						border-color: var(--el-border-color-light);
					}

					&.is-active {
						color: var(--el-color-primary);
						// 不设置背景色和边框，只改变文字颜色，与用户页面的部门列表一致
					}
				}
			}
		}
	}
}

// cl-view-group布局中的高度设置
:deep(.cl-view-group__right) {
	// 给右侧容器设置合适的高度
	height: calc(95vh - 60px); // 减去顶部导航高度
	display: flex;
	flex-direction: column;

	// 分类选择器固定高度
	.category-selector {
		flex-shrink: 0;
		padding: 16px 20px;
		border-bottom: 1px solid var(--el-border-color-extra-light);
		background-color: var(--el-bg-color);
	}

	// CRUD容器 - 三段式布局：工具栏固定 + 表格滚动 + 分页固定
	.cl-crud {
		flex: 1;
		min-height: 0;
		display: flex;
		flex-direction: column;
		overflow: hidden; // 防止整体滚动

		// 工具栏 - 固定在顶部，不滚动
		&>.cl-row:first-child {
			flex-shrink: 0; // 不收缩
			padding: 10px 0;
			border-bottom: 1px solid var(--el-border-color-extra-light);
			background-color: var(--el-bg-color);
		}

		// 表格容器 - 设置合理高度，防止溢出
		&>.cl-row:nth-child(2) {
			flex: 1; // 占用剩余空间
			min-height: 0; // 允许收缩
			overflow: hidden; // 防止溢出
			max-height: 70vh; // 限制最大高度，防止溢出

			.cl-table {
				height: 68vh; // 设置固定高度，确保不溢出

				.el-table {
					height: 100%;

					.el-table__body-wrapper {
						max-height: calc(100% - 40px); // 减去表头高度
						overflow-y: auto; // 只有表格内容滚动
					}
				}
			}
		}

		// 分页 - 固定在底部，不滚动
		&>.cl-row:last-child {
			flex-shrink: 0; // 不收缩
			margin-top: 10px; // 减少上边距
			padding: 8px 0; // 减少内边距
			border-top: 1px solid var(--el-border-color-extra-light);
			background-color: var(--el-bg-color);

			.cl-pagination {
				display: flex;
				justify-content: flex-end;
				align-items: center;
			}
		}
	}
}
</style>
