/**
 * 题目模块图片处理工具函数
 * 统一处理图片URL的格式化和路径转换
 */

/**
 * 获取当前环境的代理前缀
 * 开发环境使用 /dev，生产环境直接使用后端地址
 */
function getProxyPrefix(): string {
  // 开发环境
  if (import.meta.env.DEV) {
    return '/dev';
  }

  // 生产环境，可以根据实际部署情况调整
  // 如果前后端同域，返回空字符串
  // 如果跨域，返回完整的后端地址
  return ''; // 或者 return 'https://your-api-domain.com'
}

/**
 * 标准化图片URL
 * 处理不同环境下的图片路径，确保图片能正确显示
 * @param imageUrl 原始图片URL
 * @returns 标准化后的图片URL
 */
export function normalizeImageUrl(imageUrl: string): string {
  if (!imageUrl) return '';

  // 如果已经是完整URL，直接返回
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl;
  }

  // 如果已经包含代理前缀，直接返回
  const proxyPrefix = getProxyPrefix();
  if (proxyPrefix && imageUrl.startsWith(proxyPrefix)) {
    return imageUrl;
  }

  // 处理后端返回的完整URL（开发环境）
  if (imageUrl.includes('127.0.0.1:8001/upload/')) {
    return imageUrl.replace('http://127.0.0.1:8001', proxyPrefix);
  }

  // 处理相对路径
  if (imageUrl.startsWith('/upload/')) {
    return `${proxyPrefix}${imageUrl}`;
  }

  // 默认情况，假设是文件名，构建完整路径
  return `${proxyPrefix}/upload/images/${imageUrl}`;
}

/**
 * 批量标准化图片URL
 * @param imageUrls 图片URL数组
 * @returns 标准化后的图片URL数组
 */
export function normalizeImageUrls(imageUrls: string[]): string[] {
  return imageUrls.map(url => normalizeImageUrl(url)).filter(Boolean);
}

/**
 * 从题目内容中提取图片标签
 * @param content 题目内容
 * @returns 提取的图片文件名数组
 */
export function extractImageFromContent(content: string): string[] {
  if (!content) return [];

  // 匹配图片标签格式，支持多种格式：
  // 1. < img src="filename.png"  /> (有引号格式)
  // 2. < img src=filename.png  /> (无引号格式)
  // 3. <img src="filename.png" /> (紧凑格式)
  // 4. < img src=filename.png /> (混合格式)
  const imgRegex = /<\s*img\s+src\s*=\s*(?:"([^"]+)"|'([^']+)'|([^\s>]+))\s*[^>]*\/?>/gi;

  const images: string[] = [];
  let match;

  while ((match = imgRegex.exec(content)) !== null) {
    // srcMatch[1] 是双引号格式，srcMatch[2] 是单引号格式，srcMatch[3] 是无引号格式
    const fileName = (match[1] || match[2] || match[3] || '').trim();
    if (fileName) {
      images.push(fileName);
    }
  }

  return images;
}

/**
 * 从题目内容中移除图片标签
 * @param content 题目内容
 * @returns 清理后的纯文本内容
 */
export function removeImageTagsFromContent(content: string): string {
  if (!content) return '';

  const imgRegex = /<\s*img\s+src\s*=\s*(?:"([^"]+)"|'([^']+)'|([^\s>]+))\s*[^>]*\/?>/gi;
  return content.replace(imgRegex, '').trim();
}

/**
 * 构建分类图片URL
 * @param fileName 图片文件名
 * @param categoryId 分类ID
 * @returns 完整的图片URL
 */
export function buildCategoryImageUrl(fileName: string, categoryId?: number): string {
  if (!fileName) return '';

  // 如果已经是完整URL，直接返回
  if (fileName.startsWith('http://') || fileName.startsWith('https://')) {
    return fileName;
  }

  // 如果已经是正确的路径格式，直接返回
  if (fileName.startsWith('/upload/')) {
    return normalizeImageUrl(fileName);
  }

  // 构建分类图片路径
  const proxyPrefix = getProxyPrefix();
  const categoryPath = categoryId ? `category_${categoryId}` : 'questions';
  return `${proxyPrefix}/upload/images/${categoryPath}/${fileName}`;
}

/**
 * 检查图片URL是否有效
 * @param imageUrl 图片URL
 * @returns 是否为有效的图片URL
 */
export function isValidImageUrl(imageUrl: string): boolean {
  if (!imageUrl) return false;

  // 检查是否为有效的URL格式
  try {
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      new URL(imageUrl);
      return true;
    }

    // 检查是否为有效的相对路径
    if (imageUrl.startsWith('/')) {
      return true;
    }

    return false;
  } catch {
    return false;
  }
}

/**
 * 获取图片文件扩展名
 * @param fileName 文件名
 * @returns 文件扩展名（包含点号）
 */
export function getImageExtension(fileName: string): string {
  if (!fileName) return '';

  const lastDotIndex = fileName.lastIndexOf('.');
  if (lastDotIndex === -1) return '';

  return fileName.substring(lastDotIndex);
}

/**
 * 检查文件是否为图片格式
 * @param fileName 文件名
 * @returns 是否为图片文件
 */
export function isImageFile(fileName: string): boolean {
  if (!fileName) return false;

  const extension = getImageExtension(fileName).toLowerCase();
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];

  return imageExtensions.includes(extension);
}
