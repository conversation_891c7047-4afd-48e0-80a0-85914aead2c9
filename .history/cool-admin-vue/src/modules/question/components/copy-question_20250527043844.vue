<template>
	<el-dialog
		v-model="visible"
		title="复制题目到其他地区"
		width="500px"
		:close-on-click-modal="false"
		@close="onClose"
	>
		<div class="copy-container">
			<el-alert
				title="将当前地区和分类下的题目复制到其他地区的相同分类中"
				type="info"
				:closable="false"
				show-icon
				style="margin-bottom: 20px;"
			/>

			<el-form :model="form" label-width="120px">
				<el-form-item label="源地区分类:">
					<el-text>{{ sourceInfo }}</el-text>
				</el-form-item>

				<el-form-item label="目标地区:" required>
					<el-select
						v-model="form.targetProvinceIds"
						multiple
						placeholder="请选择目标地区"
						style="width: 100%;"
						:disabled="loading"
					>
						<el-option
							v-for="region in availableRegions"
							:key="region.id"
							:label="region.name"
							:value="region.id"
						>
							<span>{{ region.name }}</span>
							<el-tag v-if="region.type === 1" size="small" type="warning" style="margin-left: 8px;">
								直辖市
							</el-tag>
						</el-option>
					</el-select>
				</el-form-item>

				<el-form-item label="复制选项:">
					<el-checkbox-group v-model="form.options">
						<el-checkbox label="includeImages">包含图片</el-checkbox>
						<el-checkbox label="overwrite">覆盖已存在的题目</el-checkbox>
					</el-checkbox-group>
				</el-form-item>

				<el-form-item label="题目统计:">
					<el-text v-if="questionCount > 0" type="success">
						当前分类下共有 {{ questionCount }} 道题目
					</el-text>
					<el-text v-else type="warning">
						当前分类下暂无题目
					</el-text>
				</el-form-item>
			</el-form>

			<!-- 复制结果 -->
			<div v-if="copyResult" class="result-section">
				<el-divider content-position="left">复制结果</el-divider>
				<el-alert
					:title="`复制完成: 成功 ${copyResult.successCount} 个地区，失败 ${copyResult.failCount} 个地区`"
					:type="copyResult.failCount > 0 ? 'warning' : 'success'"
					:closable="false"
					show-icon
				/>

				<!-- 详细结果 -->
				<div v-if="copyResult.details.length > 0" class="copy-details">
					<el-collapse>
						<el-collapse-item title="查看详细结果" name="details">
							<el-table :data="copyResult.details" border size="small" max-height="200">
								<el-table-column prop="regionName" label="目标地区" width="120" />
								<el-table-column prop="status" label="状态" width="80">
									<template #default="{ row }">
										<el-tag :type="row.success ? 'success' : 'danger'">
											{{ row.success ? '成功' : '失败' }}
										</el-tag>
									</template>
								</el-table-column>
								<el-table-column prop="message" label="结果信息" min-width="200" />
							</el-table>
						</el-collapse-item>
					</el-collapse>
				</div>
			</div>
		</div>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="onClose">取消</el-button>
				<el-button
					type="primary"
					@click="handleCopy"
					:loading="copying"
					:disabled="form.targetProvinceIds.length === 0 || questionCount === 0"
				>
					{{ copying ? '复制中...' : '开始复制' }}
				</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
defineOptions({
	name: "copy-question",
});

import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useCool } from '/@/cool';

interface Props {
	modelValue: boolean;
	sourceProvinceId?: number;
	sourceCategoryId?: number;
}

interface Emits {
	(e: 'update:modelValue', value: boolean): void;
	(e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { service } = useCool();

const visible = computed({
	get: () => props.modelValue,
	set: (value) => emit('update:modelValue', value)
});

const form = ref({
	targetProvinceIds: [] as number[],
	options: ['includeImages'] as string[]
});

interface CopyResultItem {
	regionName: string;
	success: boolean;
	message: string;
}

const loading = ref(false);
const copying = ref(false);
const copyResult = ref<any>(null);
const regionList = ref<any[]>([]);
const questionCount = ref(0);
const sourceRegionName = ref('');
const sourceCategoryName = ref('');

// 可选择的地区（排除源地区）
const availableRegions = computed(() => {
	return regionList.value.filter(region => region.id !== props.sourceProvinceId);
});

// 源信息显示
const sourceInfo = computed(() => {
	if (sourceRegionName.value && sourceCategoryName.value) {
		return `${sourceRegionName.value} - ${sourceCategoryName.value}`;
	}
	return '未选择';
});

// 加载地区列表
async function loadRegions() {
	try {
		loading.value = true;
		const res = await service.region.region.list();
		const actualData = Array.isArray(res) ? res : ((res as any)?.data || []);
		regionList.value = actualData.filter((item: any) => item.type === 0 || item.type === 1);
	} catch (error) {
		console.error('加载地区列表失败:', error);
		ElMessage.error('加载地区列表失败');
	} finally {
		loading.value = false;
	}
}

// 获取源地区和分类信息
async function loadSourceInfo() {
	if (!props.sourceProvinceId || !props.sourceCategoryId) return;

	try {
		// 获取地区名称
		const region = regionList.value.find(r => r.id === props.sourceProvinceId);
		sourceRegionName.value = region ? region.name : '';

		// 获取分类名称
		const categoryRes = await service.category.category.info({
			id: props.sourceCategoryId
		});
		sourceCategoryName.value = categoryRes.name || '';

		// 获取题目数量
		const questionRes = await service.question.question.page({
			provinceId: props.sourceProvinceId,
			categoryId: props.sourceCategoryId,
			size: 1
		});
		questionCount.value = questionRes.pagination?.total || 0;

	} catch (error) {
		console.error('加载源信息失败:', error);
	}
}

// 执行复制
async function handleCopy() {
	if (form.value.targetProvinceIds.length === 0) {
		ElMessage.error('请选择目标地区');
		return;
	}

	if (questionCount.value === 0) {
		ElMessage.error('当前分类下没有题目可复制');
		return;
	}

	copying.value = true;

	try {
		// 获取源题目列表
		const questionsRes = await service.question.question.listWithOptions({
			provinceId: props.sourceProvinceId,
			categoryId: props.sourceCategoryId
		});

		const sourceQuestions = questionsRes.data || questionsRes;
		if (!sourceQuestions || sourceQuestions.length === 0) {
			ElMessage.error('没有找到可复制的题目');
			return;
		}

		const results: CopyResultItem[] = [];

		// 逐个地区复制
		for (const targetProvinceId of form.value.targetProvinceIds) {
			try {
				const targetRegion = regionList.value.find(r => r.id === targetProvinceId);

				// 复制题目到目标地区
				const copyRes = await service.question.question.request({
					url: '/copyToProvince',
					method: 'POST',
					data: {
						questionIds: sourceQuestions.map((q: any) => q.id),
						targetProvinceId: targetProvinceId
					}
				});

				const copyResults = copyRes.data || copyRes;
				const successCount = copyResults.filter((r: any) => r.success).length;

				results.push({
					regionName: targetRegion?.name || `地区${targetProvinceId}`,
					success: successCount > 0,
					message: `成功复制 ${successCount}/${sourceQuestions.length} 道题目`
				});

			} catch (error: any) {
				const targetRegion = regionList.value.find(r => r.id === targetProvinceId);
				results.push({
					regionName: targetRegion?.name || `地区${targetProvinceId}`,
					success: false,
					message: `复制失败: ${error?.message || '未知错误'}`
				});
			}
		}

		// 统计结果
		const successCount = results.filter(r => r.success).length;
		const failCount = results.length - successCount;

		copyResult.value = {
			successCount,
			failCount,
			details: results
		};

		if (failCount === 0) {
			ElMessage.success(`成功复制到 ${successCount} 个地区`);
			emit('success');
		} else {
			ElMessage.warning(`复制完成，成功 ${successCount} 个，失败 ${failCount} 个`);
		}

	} catch (error) {
		console.error('复制失败:', error);
		ElMessage.error('复制失败，请稍后重试');
	} finally {
		copying.value = false;
	}
}

// 关闭对话框
function onClose() {
	visible.value = false;
	// 重置状态
	form.value.targetProvinceIds = [];
	form.value.options = ['includeImages'];
	copyResult.value = null;
}

// 监听对话框打开
watch(visible, (newVal) => {
	if (newVal) {
		// 重置状态
		form.value.targetProvinceIds = [];
		form.value.options = ['includeImages'];
		copyResult.value = null;

		// 加载数据
		loadSourceInfo();
	}
});

onMounted(() => {
	loadRegions();
});
</script>

<style lang="scss" scoped>
.copy-container {
	.result-section {
		margin-top: 20px;

		.copy-details {
			margin-top: 12px;
		}
	}
}
</style>
