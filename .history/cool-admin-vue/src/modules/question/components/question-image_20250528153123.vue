<template>
	<div class="question-image">
		<!-- 显示模式 -->
		<div v-if="!isEdit" class="image-display">
			<cl-image
				v-if="displayUrl"
				:src="displayUrl"
				:size="size"
				:preview="preview"
				:lazy="lazy"
				fit="cover"
			/>
			<div v-else class="no-image">
				<el-icon><picture /></el-icon>
				<span>暂无图片</span>
			</div>
		</div>

		<!-- 编辑模式 -->
		<div v-else class="image-edit">
			<cl-upload
				v-model="imageUrl"
				type="image"
				:accept="accept"
				:limit="1"
				:limit-size="limitSize"
				:size="uploadSize"
				:text="text"
				:show-file-list="showFileList"
				:multiple="false"
				:upload-url="uploadUrl"
				@update:modelValue="onImageChange"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { Picture } from '@element-plus/icons-vue';
import { normalizeImageUrl } from '../utils/image-utils';

interface Props {
	modelValue?: string;
	categoryId?: number;
	size?: number | [number, number];
	preview?: boolean;
	lazy?: boolean;
	isEdit?: boolean;
	accept?: string;
	limitSize?: number;
	text?: string;
	showFileList?: boolean;
}

interface Emits {
	(e: 'update:modelValue', value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
	modelValue: '',
	categoryId: 0,
	size: 40,
	preview: true,
	lazy: true,
	isEdit: false,
	accept: 'image/*',
	limitSize: 2,
	text: '选择图片',
	showFileList: true
});

const emit = defineEmits<Emits>();

// 内部图片URL状态
const imageUrl = ref(props.modelValue);

// 上传组件的尺寸
const uploadSize = computed(() => {
	if (Array.isArray(props.size)) {
		return [Math.max(props.size[0], 120), Math.max(props.size[1], 120)];
	}
	return [120, 120];
});

// 显示用的图片URL（自动处理环境前缀）
const displayUrl = computed(() => {
	return normalizeImageUrl(imageUrl.value);
});

// 上传URL
const uploadUrl = computed(() => {
	if (props.categoryId) {
		return `/admin/question/question/uploadCategoryImage?categoryId=${props.categoryId}`;
	}
	return '/admin/question/question/uploadImage';
});

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
	imageUrl.value = newValue;
}, { immediate: true });

// 图片变化处理
function onImageChange(value: string) {
	imageUrl.value = value;
	emit('update:modelValue', value);
}
</script>

<style lang="scss" scoped>
.question-image {
	.image-display {
		.no-image {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			width: 40px;
			height: 40px;
			border: 1px dashed var(--el-border-color);
			border-radius: 4px;
			color: var(--el-text-color-placeholder);
			font-size: 12px;

			.el-icon {
				font-size: 16px;
				margin-bottom: 2px;
			}

			span {
				font-size: 10px;
			}
		}
	}

	.image-edit {
		:deep(.cl-upload) {
			.el-upload {
				border: 1px dashed var(--el-border-color);
				border-radius: 6px;
				cursor: pointer;
				position: relative;
				overflow: hidden;
				transition: var(--el-transition-duration-fast);

				&:hover {
					border-color: var(--el-color-primary);
				}
			}

			.el-upload-list {
				margin-top: 8px;

				.el-upload-list__item {
					.el-upload-list__item-thumbnail {
						width: 60px;
						height: 60px;
						object-fit: cover;
						border-radius: 4px;
					}
				}
			}
		}
	}
}
</style>
