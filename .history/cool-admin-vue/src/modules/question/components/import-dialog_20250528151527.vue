<template>
	<el-dialog
		v-model="visible"
		title="批量导入题目"
		width="600px"
		:close-on-click-modal="false"
		@close="onClose"
	>
		<div class="import-container">
			<!-- 导入说明 -->
			<div class="import-info">
				<el-alert
					type="info"
					:closable="false"
					show-icon
				>
					<template #default>
						<div class="import-content">
							<div class="target-item">
								<span class="label">省份：</span>
								<el-tag type="primary" size="default">{{ props.provinceName || '未选择' }}</el-tag>
							</div>
							<div class="target-item">
								<span class="label">分类：</span>
								<el-tag
									type="success"
									size="default"
									class="category-tag"
									:title="props.categoryName"
								>
									{{ props.categoryName || '未选择' }}
								</el-tag>
							</div>
							<div class="rules-text">
								题目将导入到该分类对应的科目中，用户开通科目后可见。
							</div>
						</div>
					</template>
				</el-alert>
			</div>

			<!-- Tab切换 -->
			<el-tabs v-model="activeTab" class="import-tabs">
				<!-- 文件导入Tab -->
				<el-tab-pane label="文件导入" name="file">
					<!-- 文件上传区域 -->
					<el-upload
						ref="uploadRef"
						class="upload-demo"
						drag
						:auto-upload="false"
						:on-change="handleFileChange"
						:before-upload="beforeUpload"
						accept=".xlsx,.xls,.csv"
						:limit="1"
					>
						<el-icon class="el-icon--upload"><upload-filled /></el-icon>
						<div class="el-upload__text">
							将文件拖到此处，或<em>点击上传</em>
						</div>
						<template #tip>
							<div class="el-upload__tip">
								支持 Excel (.xlsx, .xls) 和 CSV (.csv) 格式文件
							</div>
						</template>
					</el-upload>

					<!-- 文件信息 -->
					<div v-if="selectedFile" class="file-info">
						<el-alert
							:title="`已选择文件: ${selectedFile.name}`"
							type="info"
							:closable="false"
							show-icon
						/>
					</div>
				</el-tab-pane>

				<!-- 图片上传Tab -->
				<el-tab-pane label="批量上传图片" name="image">
					<el-alert
						type="info"
						:closable="false"
						show-icon
						class="upload-tips-alert"
					>
						<template #default>
							<div class="upload-tips">
								<p><strong>上传说明：</strong></p>
								<ul>
									<li>图片将上传到分类文件夹：<code>/uploads/images/category_{{ props.categoryId }}/</code></li>
									<li>请确保图片文件名与题目中的图片标签一致</li>
									<li>支持 JPG、PNG、GIF 格式，单个文件不超过 2MB</li>
									<li>可以一次选择多个图片文件进行批量上传</li>
								</ul>
							</div>
						</template>
					</el-alert>

					<!-- 使用图片上传组件进行批量上传 -->
					<div class="batch-image-upload">
						<cl-upload
							v-model="batchImageUrls"
							type="image"
							:size="[120, 120]"
							:limit="50"
							:limit-size="2"
							text="批量选择图片"
							:multiple="true"
							:show-file-list="true"
							:drag="true"
							:upload-url="`/admin/question/question/uploadCategoryImage?categoryId=${props.categoryId}`"
							@success="onBatchUploadSuccess"
							@error="onBatchUploadError"
						/>
					</div>

					<!-- 图片上传结果 -->
					<div v-if="imageUploadResult" class="image-upload-result">
						<el-alert
							:title="`图片上传完成: 成功 ${imageUploadResult.successCount} 个，失败 ${imageUploadResult.failCount} 个`"
							:type="imageUploadResult.failCount > 0 ? 'warning' : 'success'"
							:closable="false"
							show-icon
						>
							<template #default v-if="imageUploadResult.failures.length > 0">
								<el-collapse>
									<el-collapse-item title="查看失败详情" name="image-failures">
										<ul>
											<li v-for="failure in imageUploadResult.failures" :key="failure.filename">
												{{ failure.filename }}: {{ failure.error }}
											</li>
										</ul>
									</el-collapse-item>
								</el-collapse>
							</template>
						</el-alert>
					</div>
				</el-tab-pane>
			</el-tabs>

			<!-- 数据预览 -->
			<div v-if="previewData.length > 0" class="preview-section">
				<el-divider content-position="left">数据预览 (前5条)</el-divider>
				<el-table :data="previewData.slice(0, 5)" border size="small" max-height="400">
					<el-table-column :label="getColumnLabel('type')" width="80">
						<template #default="{ row }">
							{{ getFieldValue(row, ['题型', 'type', '题目类型']) }}
						</template>
					</el-table-column>
					<el-table-column :label="getColumnLabel('content')" min-width="200" show-overflow-tooltip>
						<template #default="{ row }">
							{{ getFieldValue(row, ['题目', 'content', '题目内容', '问题']) }}
						</template>
					</el-table-column>
					<el-table-column :label="getColumnLabel('options')" min-width="150" show-overflow-tooltip>
						<template #default="{ row }">
							{{ getFieldValue(row, ['答案选项', 'options', '选项', '答案']) }}
						</template>
					</el-table-column>
					<el-table-column :label="getColumnLabel('correctAnswer')" width="100">
						<template #default="{ row }">
							{{ getFieldValue(row, ['正确答案', 'correctAnswer', '答案', '正确选项']) }}
						</template>
					</el-table-column>
					<el-table-column :label="getColumnLabel('analysis')" min-width="150" show-overflow-tooltip>
						<template #default="{ row }">
							{{ getFieldValue(row, ['答题解析', 'analysis', '解析', '说明']) }}
						</template>
					</el-table-column>
					<el-table-column :label="getColumnLabel('score')" width="80">
						<template #default="{ row }">
							{{ getFieldValue(row, ['分数', 'score', '题目分数', '分值']) }}
						</template>
					</el-table-column>
					<el-table-column :label="getColumnLabel('difficulty')" width="80">
						<template #default="{ row }">
							{{ getFieldValue(row, ['难度', 'difficulty', '难度级别']) }}
						</template>
					</el-table-column>
				</el-table>
				<div class="preview-info">
					<el-text type="info">共 {{ previewData.length }} 条数据</el-text>
				</div>
			</div>

			<!-- 导入结果 -->
			<div v-if="importResult" class="result-section">
				<el-divider content-position="left">导入结果</el-divider>
				<el-alert
					:title="getImportResultTitle()"
					:type="importResult.failCount > 0 ? 'warning' : 'success'"
					:closable="false"
					show-icon
				>
					<template #default v-if="importResult.subjectInfo">
						<p>科目：{{ importResult.subjectInfo.subjectName }}</p>
					</template>
				</el-alert>

				<!-- 失败详情 -->
				<div v-if="importResult.failures.length > 0" class="failure-details">
					<el-collapse>
						<el-collapse-item title="查看失败详情" name="failures">
							<el-table :data="importResult.failures" border size="small" max-height="200">
								<el-table-column prop="row" label="行号" width="80" />
								<el-table-column prop="error" label="错误信息" min-width="200" />
							</el-table>
						</el-collapse-item>
					</el-collapse>
				</div>
			</div>
		</div>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="onClose">取消</el-button>
				<el-button
					type="primary"
					@click="handleImport"
					:loading="importing"
					:disabled="!selectedFile || previewData.length === 0"
				>
					{{ importing ? '导入中...' : '开始导入' }}
				</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script lang="ts" setup>
defineOptions({
	name: "import-dialog",
});
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { UploadFilled } from '@element-plus/icons-vue';
import { useCool } from '/@/cool';
import * as XLSX from 'xlsx';

interface Props {
	modelValue: boolean;
	provinceId?: number;
	categoryId?: number;
	provinceName?: string;
	categoryName?: string;
}

interface Emits {
	(e: 'update:modelValue', value: boolean): void;
	(e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { service } = useCool();

const visible = computed({
	get: () => props.modelValue,
	set: (value) => emit('update:modelValue', value)
});

const uploadRef = ref();
const selectedFile = ref<File | null>(null);
const previewData = ref<any[]>([]);
const importing = ref(false);
const importResult = ref<any>(null);

// Tab状态
const activeTab = ref('file');

// 图片上传相关状态
const batchImageUrls = ref<string[]>([]);
const imageUploadResult = ref<any>(null);

// 文件选择处理
function handleFileChange(file: any) {
	selectedFile.value = file.raw;
	parseFile(file.raw);
}

// 文件上传前验证
function beforeUpload(file: File) {
	const isValidType = file.name.endsWith('.xlsx') || file.name.endsWith('.xls') || file.name.endsWith('.csv');
	if (!isValidType) {
		ElMessage.error('只支持 Excel 和 CSV 格式文件');
		return false;
	}

	const isLt10M = file.size / 1024 / 1024 < 10;
	if (!isLt10M) {
		ElMessage.error('文件大小不能超过 10MB');
		return false;
	}

	return false; // 阻止自动上传
}

// 解析文件
async function parseFile(file: File) {
	try {
		if (file.name.endsWith('.csv')) {
			await parseCSV(file);
		} else {
			await parseExcel(file);
		}
	} catch (error) {
		console.error('文件解析失败:', error);
		ElMessage.error('文件解析失败，请检查文件格式');
		previewData.value = [];
	}
}

// 解析CSV文件
async function parseCSV(file: File) {
	const text = await file.text();
	const lines = text.trim().split('\n');

	if (lines.length < 2) {
		throw new Error('CSV文件至少需要包含标题行和数据行');
	}

	const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
	const data: any[] = [];

	for (let i = 1; i < lines.length; i++) {
		const values = parseCSVLine(lines[i]);
		const row: any = {};
		headers.forEach((header, index) => {
			row[header] = values[index] || '';
		});
		data.push(row);
	}

	previewData.value = data;
}

// 解析CSV行
function parseCSVLine(line: string): string[] {
	const result: string[] = [];
	let current = '';
	let inQuotes = false;

	for (let i = 0; i < line.length; i++) {
		const char = line[i];

		if (char === '"') {
			inQuotes = !inQuotes;
		} else if (char === ',' && !inQuotes) {
			result.push(current.trim());
			current = '';
		} else {
			current += char;
		}
	}

	result.push(current.trim());
	return result;
}

// 解析Excel文件
async function parseExcel(file: File) {
	const buffer = await file.arrayBuffer();
	const workbook = XLSX.read(buffer, { type: 'array' });
	const sheetName = workbook.SheetNames[0];
	const worksheet = workbook.Sheets[sheetName];
	const data: any[] = XLSX.utils.sheet_to_json(worksheet);

	previewData.value = data;
}

// 批量图片上传成功回调
function onBatchUploadSuccess(urls: string[]) {
	const successCount = urls.length;
	imageUploadResult.value = {
		successCount,
		failCount: 0,
		failures: []
	};
	ElMessage.success(`成功上传 ${successCount} 个图片`);
}

// 批量图片上传失败回调
function onBatchUploadError(error: any) {
	console.error('批量上传失败:', error);
	imageUploadResult.value = {
		successCount: 0,
		failCount: 1,
		failures: [{ filename: '未知文件', error: error.message || '上传失败' }]
	};
	ElMessage.error('批量上传失败，请稍后重试');
}

// 执行导入
async function handleImport() {
	if (!props.provinceId || !props.categoryId) {
		ElMessage.error('缺少省份或分类信息');
		return;
	}

	if (previewData.value.length === 0) {
		ElMessage.error('没有可导入的数据');
		return;
	}

	importing.value = true;

	try {
		console.log('=== 开始导入题目 ===');
		console.log('导入数据条数:', previewData.value.length);
		console.log('省份ID:', props.provinceId);
		console.log('分类ID:', props.categoryId);
		console.log('前3条数据示例:', previewData.value.slice(0, 3));

		const res = await service.question.question.request({
			url: '/importExcel',
			method: 'POST',
			data: {
				data: previewData.value,
				provinceId: props.provinceId,
				categoryId: props.categoryId
			}
		});

		console.log('导入响应:', res);

		// 处理导入结果
		const results = res.data || res;
		const successCount = results.filter((r: any) => r.success).length;
		const failures = results.filter((r: any) => !r.success);

		// 获取科目信息（从成功的结果中获取）
		const successResults = results.filter((r: any) => r.success);
		const subjectInfo = successResults.length > 0 ? {
			subjectId: successResults[0].subjectId,
			subjectName: successResults[0].subjectName
		} : null;

		importResult.value = {
			successCount,
			failCount: failures.length,
			subjectInfo,
			failures: failures.map((f: any) => ({
				row: f.row,
				error: f.error
			}))
		};

		if (failures.length === 0) {
			const message = subjectInfo
				? `成功导入 ${successCount} 条题目到科目"${subjectInfo.subjectName}"`
				: `成功导入 ${successCount} 条题目`;
			ElMessage.success(message);
			emit('success');
		} else {
			const message = subjectInfo
				? `导入完成，成功 ${successCount} 条，失败 ${failures.length} 条（科目：${subjectInfo.subjectName}）`
				: `导入完成，成功 ${successCount} 条，失败 ${failures.length} 条`;
			ElMessage.warning(message);
		}

	} catch (error) {
		console.error('导入失败:', error);
		ElMessage.error('导入失败，请稍后重试');
	} finally {
		importing.value = false;
	}
}

// 获取字段值（支持多种列名）
function getFieldValue(row: any, fieldNames: string[]): any {
	for (const fieldName of fieldNames) {
		if (row[fieldName] !== undefined && row[fieldName] !== null && row[fieldName] !== '') {
			return row[fieldName];
		}
	}
	return '';
}

// 获取列标签
function getColumnLabel(type: string): string {
	const labels = {
		type: '题型',
		content: '题目内容',
		options: '答案选项',
		correctAnswer: '正确答案',
		analysis: '答题解析',
		score: '分数',
		difficulty: '难度'
	};
	return labels[type as keyof typeof labels] || type;
}

// 获取导入结果标题
function getImportResultTitle(): string {
	if (!importResult.value) return '';
	return `导入完成: 成功 ${importResult.value.successCount} 条，失败 ${importResult.value.failCount} 条`;
}

// 关闭对话框
function onClose() {
	visible.value = false;
	// 重置状态
	selectedFile.value = null;
	previewData.value = [];
	importResult.value = null;
	if (uploadRef.value) {
		uploadRef.value.clearFiles();
	}

	// 重置图片上传状态
	batchImageUrls.value = [];
	imageUploadResult.value = null;
}

// 监听对话框打开
watch(visible, (newVal) => {
	if (newVal) {
		console.log('=== 导入对话框打开 ===');
		console.log('Props:', props);
		console.log('省份名称:', props.provinceName);
		console.log('分类名称:', props.categoryName);
		// 重置状态
		selectedFile.value = null;
		previewData.value = [];
		importResult.value = null;
		if (uploadRef.value) {
			uploadRef.value.clearFiles();
		}
	}
});
</script>

<style lang="scss" scoped>
.import-container {
	.import-info {
		margin-bottom: 20px;

		.import-content {
			.target-item {
				display: flex;
				align-items: center;
				gap: 8px;
				margin-bottom: 8px;

				&:last-child {
					margin-bottom: 12px;
				}

				.label {
					font-size: 15px;
					color: var(--el-text-color-regular);
					min-width: 30px;
					font-weight: 600;
				}

				.category-tag {
					max-width: 400px;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
			}

			.rules-text {
				font-size: 13px;
				color: var(--el-text-color-secondary);
				line-height: 1.4;
				margin-top: 4px;
			}
		}
	}

	.file-info {
		margin: 16px 0;
	}

	.preview-section {
		margin-top: 20px;

		.preview-info {
			margin-top: 8px;
			text-align: right;
		}
	}

	.result-section {
		margin-top: 20px;

		.failure-details {
			margin-top: 12px;
		}
	}
}

.upload-demo {
	margin-bottom: 16px;
}

.import-tabs {
	margin-top: 20px;

	.el-tabs__content {
		padding-top: 20px;
	}

	.upload-tips-alert {
		margin-bottom: 16px;
	}
}

.image-upload-section {
	margin-top: 20px;

	.upload-tips {
		ul {
			margin: 8px 0;
			padding-left: 20px;

			li {
				margin-bottom: 4px;
				font-size: 13px;

				code {
					background: var(--el-fill-color-light);
					padding: 2px 4px;
					border-radius: 3px;
					font-family: monospace;
					font-size: 12px;
				}
			}
		}
	}

	.image-upload-demo {
		margin: 16px 0;
	}

	.image-upload-actions {
		display: flex;
		gap: 12px;
		margin-top: 12px;
		justify-content: flex-start;
	}

	.image-upload-result {
		margin-top: 16px;

		ul {
			margin: 8px 0;
			padding-left: 20px;

			li {
				margin-bottom: 4px;
				font-size: 13px;
				color: var(--el-color-danger);
			}
		}
	}

	// 批量图片上传样式
	.batch-image-upload {
		margin: 16px 0;

		:deep(.cl-upload) {
			.el-upload-dragger {
				width: 100%;
				height: 180px;
				border: 2px dashed var(--el-border-color);
				border-radius: 6px;
				cursor: pointer;
				position: relative;
				overflow: hidden;
				transition: var(--el-transition-duration-fast);

				&:hover {
					border-color: var(--el-color-primary);
				}
			}

			.el-upload__text {
				color: var(--el-text-color-regular);
				font-size: 14px;
				text-align: center;
			}

			.el-upload-list {
				margin-top: 16px;

				.el-upload-list__item {
					transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);

					.el-upload-list__item-thumbnail {
						width: 60px;
						height: 60px;
						object-fit: cover;
						border-radius: 4px;
					}
				}
			}
		}
	}
}

// 响应式设计
@media (max-width: 768px) {
	.import-container {
		.import-info {
			flex-direction: column;
			gap: 12px;
		}

		.image-upload-section {
			.image-upload-actions {
				flex-direction: column;

				.el-button {
					width: 100%;
				}
			}
		}
	}
}
</style>
