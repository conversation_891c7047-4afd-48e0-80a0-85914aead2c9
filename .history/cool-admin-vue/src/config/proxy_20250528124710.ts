// 获取后端服务地址，支持环境变量配置
const getBackendUrl = () => {
	return process.env.VITE_API_URL || 'http://127.0.0.1:8001';
};

const proxy = {
	'/dev/': {
		target: getBackendUrl(),
		changeOrigin: true,
		rewrite: (path: string) => path.replace(/^\/dev/, '')
	},

	// 静态文件代理 - 用于图片等资源访问，自动适配后端地址
	'/upload/': {
		target: getBackendUrl(),
		changeOrigin: true
	},

	'/prod/': {
		target: 'https://show.cool-admin.com',
		changeOrigin: true,
		rewrite: (path: string) => path.replace(/^\/prod/, '/api')
	}
};

const value = 'dev';
const host = proxy[`/${value}/`]?.target;

export { proxy, host, value };
