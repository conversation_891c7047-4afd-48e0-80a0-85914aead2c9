<template>
	<div class="question-options" data-component="question-options">

		<!-- 单选题和多选题选项 -->
		<div v-if="questionType === 0 || questionType === 1" class="choice-options">
			<div class="options-header">
				<el-text class="label">{{ questionType === 0 ? '单选题选项' : '多选题选项' }}</el-text>
			</div>

			<div class="options-list">
				<div
					v-for="(option, index) in options"
					:key="index"
					class="option-row"
				>
					<el-tag :type="getOptionTagType(option.label)" size="small" class="option-label">
						{{ option.label }}
					</el-tag>

					<div class="option-input-container">

						<el-input
							v-model="option.content"
							placeholder="请输入选项内容"
							clearable
							class="option-content"
							@input="updateOption(index, 'content', $event)"
						>
							<template #append>
								<el-button
									size="small"
									type="primary"
									text
									@click="addOption"
									:disabled="options.length >= 6"
									v-if="index === options.length - 1"
								>
									<el-icon><Plus /></el-icon>
								</el-button>
								<el-button
									size="small"
									type="danger"
									text
									@click="removeOption(index)"
									:disabled="options.length <= 2"
								>
									<el-icon><Minus /></el-icon>
								</el-button>
							</template>
						</el-input>

						<!-- 选项图片上传 -->
						<cl-upload
							v-model="option.image"
							type="image"
							:size="[60, 60]"
							:limit="1"
							:limit-size="2"
							text="选项图片"
							:multiple="false"
							@update:modelValue="updateOption(index, 'image', $event)"
						/>
					</div>
				</div>
			</div>

			<!-- 正确答案选择 -->
			<div class="correct-answer-section">
				<span class="correct-answer-label">正确答案：</span>
				<!-- 多选题 -->
				<el-checkbox-group
					v-if="questionType === 1"
					v-model="correctMultipleAnswers"
					@change="updateMultipleCorrectAnswers"
					class="answer-group"
				>
					<el-checkbox
						v-for="option in options"
						:key="option.label"
						:value="option.label"
					>
						{{ option.label }}
					</el-checkbox>
				</el-checkbox-group>

				<!-- 单选题和判断题 -->
				<el-radio-group
					v-else
					v-model="correctSingleAnswer"
					@change="updateSingleCorrectAnswer"
					class="answer-group"
				>
					<el-radio
						v-for="option in options"
						:key="option.label"
						:value="option.label"
					>
						{{ option.label }}{{ option.content ? ` (${option.content})` : '' }}
					</el-radio>
				</el-radio-group>
			</div>
		</div>

		<!-- 判断题选项显示 -->
		<div v-else-if="questionType === 2" class="judge-options">
			<div class="judge-display">
				<div class="judge-options-list">
					<div class="judge-option">
						<el-tag type="primary" size="small">A</el-tag>
						<span>正确</span>
					</div>
					<div class="judge-option">
						<el-tag type="success" size="small">B</el-tag>
						<span>错误</span>
					</div>
				</div>
			</div>

			<!-- 正确答案选择 -->
			<div class="correct-answer-section">
				<span class="correct-answer-label">正确答案：</span>
				<el-radio-group v-model="correctSingleAnswer" @change="updateSingleCorrectAnswer" class="answer-group">
					<el-radio value="A">A (正确)</el-radio>
					<el-radio value="B">B (错误)</el-radio>
				</el-radio-group>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
defineOptions({
	name: "question-options",
});
import { ref, watch, computed, onMounted } from 'vue';
import { Plus, Minus } from '@element-plus/icons-vue';

interface QuestionOption {
	label: string;
	content: string;
	image: string;
	isCorrect: boolean;
	orderNum: number;
}

interface Props {
	modelValue?: QuestionOption[];
	questionType?: number; // 直接通过props传递题目类型
	categoryId?: number; // 分类ID，用于图片上传路径
}

interface Emits {
	(e: 'update:modelValue', value: QuestionOption[]): void;
	(e: 'update:correctAnswer', value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
	modelValue: () => [],
	questionType: 0,
	categoryId: 0
});
const emit = defineEmits<Emits>();

// 使用props中的题目类型
const questionType = computed(() => props.questionType || 0);

const options = ref<QuestionOption[]>([]);
const correctSingleAnswer = ref<string>('');
const correctMultipleAnswers = ref<string[]>([]);
const judgeAnswer = ref<string>('A');

// 选项标签
const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];



// 创建默认选项
function createDefaultOptions() {
	if (questionType.value === 0 || questionType.value === 1) {
		// 单选题和多选题默认创建4个选项
		options.value = optionLabels.slice(0, 4).map((label, index) => ({
			label,
			content: '',
			image: '',
			isCorrect: index === 0, // 默认第一个为正确答案
			orderNum: index + 1,
		}));

		if (questionType.value === 0) {
			correctSingleAnswer.value = 'A';
		} else {
			correctMultipleAnswers.value = ['A'];
		}
	} else if (questionType.value === 2) {
		// 判断题创建固定的正确/错误选项
		options.value = [
			{
				label: 'A',
				content: '正确',
				image: '',
				isCorrect: true,
				orderNum: 1,
			},
			{
				label: 'B',
				content: '错误',
				image: '',
				isCorrect: false,
				orderNum: 2,
			}
		];
		judgeAnswer.value = 'A';
		correctSingleAnswer.value = 'A';
	}
}

// 添加选项
function addOption() {
	if (options.value.length >= 6) return;

	const newLabel = optionLabels[options.value.length];
	options.value.push({
		label: newLabel,
		content: '',
		image: '',
		isCorrect: false,
		orderNum: options.value.length + 1,
	});

	emitUpdate();
}

// 删除选项
function removeOption(index: number) {
	if (options.value.length <= 2) return;

	const removedOption = options.value[index];
	options.value.splice(index, 1);

	// 重新分配标签
	options.value.forEach((option, idx) => {
		option.label = optionLabels[idx];
		option.orderNum = idx + 1;
	});

	// 如果删除的是正确答案，重置正确答案
	if (questionType.value === 0 && removedOption.isCorrect) {
		correctSingleAnswer.value = options.value.length > 0 ? options.value[0].label : '';
		updateSingleCorrectAnswer();
	} else if (questionType.value === 1 && removedOption.isCorrect) {
		// 多选题：从正确答案数组中移除被删除的选项
		correctMultipleAnswers.value = correctMultipleAnswers.value.filter(
			label => label !== removedOption.label
		);
		// 重新映射标签
		const newCorrectAnswers: string[] = [];
		correctMultipleAnswers.value.forEach(oldLabel => {
			const oldIndex = optionLabels.indexOf(oldLabel);
			if (oldIndex < options.value.length) {
				newCorrectAnswers.push(optionLabels[oldIndex]);
			}
		});
		correctMultipleAnswers.value = newCorrectAnswers;
		updateMultipleCorrectAnswers();
	}

	emitUpdate();
}

// 更新选项
function updateOption(index: number, field: keyof QuestionOption, value: any) {
	if (options.value[index]) {
		(options.value[index] as any)[field] = value;
		emitUpdate();
	}
}

// 更新单选题正确答案
function updateSingleCorrectAnswer() {
	options.value.forEach(option => {
		option.isCorrect = option.label === correctSingleAnswer.value;
	});
	// 同步更新表单的correctAnswer字段
	emit('update:correctAnswer', correctSingleAnswer.value);
	emitUpdate();
}

// 更新多选题正确答案
function updateMultipleCorrectAnswers() {
	options.value.forEach(option => {
		option.isCorrect = correctMultipleAnswers.value.includes(option.label);
	});
	// 同步更新表单的correctAnswer字段（多选用逗号分隔）
	emit('update:correctAnswer', correctMultipleAnswers.value.join(','));
	emitUpdate();
}



// 发送更新事件
function emitUpdate() {
	emit('update:modelValue', [...options.value]);
}



// 获取选项标签类型
function getOptionTagType(label: string) {
	const types = ['primary', 'success', 'warning', 'danger', 'info', ''] as const;
	const index = optionLabels.indexOf(label);
	return types[index] || 'primary';
}



// 监听题目类型变化 - 符合Cool Admin Vue规范
watch(() => props.questionType, (newType, oldType) => {
	// 只有在新增题目时（没有外部数据且选项为空）才重置
	if (newType !== oldType && newType !== undefined) {
		// 如果有外部传入的数据，或者当前已有有效选项数据，不要重置
		const hasExternalData = props.modelValue && props.modelValue.length > 0;
		const hasValidOptions = options.value.length > 0 && options.value.some(opt => opt.content && opt.content.trim() !== '');

		if (hasExternalData || hasValidOptions) {
			return;
		}

		// 重置所有答案状态
		correctSingleAnswer.value = '';
		correctMultipleAnswers.value = [];
		judgeAnswer.value = 'A';

		createDefaultOptions();
		emitUpdate();
	}
}, { immediate: false });

// 监听外部数据变化 - 符合Cool Admin Vue规范
watch(() => props.modelValue, (newValue) => {
	if (newValue && newValue.length > 0) {
		// 直接使用传入的数据
		options.value = [...newValue];

		// 设置正确答案
		if (questionType.value === 0) {
			const correctOption = options.value.find(opt => opt.isCorrect);
			correctSingleAnswer.value = correctOption?.label || '';
		} else if (questionType.value === 1) {
			const correctOptions = options.value.filter(opt => opt.isCorrect);
			correctMultipleAnswers.value = correctOptions.map(opt => opt.label);
		} else if (questionType.value === 2) {
			const correctOption = options.value.find(opt => opt.isCorrect);
			judgeAnswer.value = correctOption?.label || 'A';
			correctSingleAnswer.value = judgeAnswer.value;
		}
	} else {
		// 如果没有数据且题目类型已设置，创建默认选项
		if (questionType.value !== undefined && options.value.length === 0) {
			createDefaultOptions();
			emitUpdate();
		}
	}
}, { deep: true, immediate: true });

// 暴露方法给父组件直接调用 - 解决Cool Admin Vue数据传递问题
function forceSetOptions(optionsData: QuestionOption[]) {
	if (optionsData && optionsData.length > 0) {
		options.value = optionsData.map(item => ({
			label: item.label || '',
			content: item.content || '',
			image: item.image || '',
			isCorrect: item.isCorrect || false,
			orderNum: item.orderNum || 0
		}));

		// 设置正确答案
		if (questionType.value === 0) {
			const correctOption = options.value.find(opt => opt.isCorrect);
			correctSingleAnswer.value = correctOption?.label || '';
		} else if (questionType.value === 1) {
			const correctOptions = options.value.filter(opt => opt.isCorrect);
			correctMultipleAnswers.value = correctOptions.map(opt => opt.label);
		} else if (questionType.value === 2) {
			const correctOption = options.value.find(opt => opt.isCorrect);
			judgeAnswer.value = correctOption?.label || 'A';
			correctSingleAnswer.value = judgeAnswer.value;
		}

		emitUpdate();
	}
}

// 组件挂载时初始化 - 符合Cool Admin Vue规范
onMounted(() => {
	if (props.modelValue && props.modelValue.length > 0) {
		// 如果有外部数据，使用外部数据初始化
		options.value = [...props.modelValue];
	} else {
		// 否则创建默认选项
		createDefaultOptions();
		emitUpdate();
	}
});

// 暴露方法给父组件
defineExpose({
	forceSetOptions
});
</script>

<style lang="scss" scoped>
.question-options {
	.options-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16px;

		.label {
			font-weight: 600;
			font-size: 14px;
		}
	}

	.options-list {
		.option-row {
			display: flex;
			align-items: flex-start;
			gap: 8px;
			margin-bottom: 12px;

			.option-label {
				flex-shrink: 0;
				margin-top: 8px; // 与输入框对齐
			}

			.option-input-container {
				flex: 1;
				display: flex;
				flex-direction: column;
				gap: 8px;

				.option-content {
					width: 100%;
				}

				:deep(.el-input-group__append) {
					padding: 0 4px;

					.el-button {
						margin: 0 2px;
						padding: 4px;

						.el-icon {
							font-size: 14px;
						}
					}
				}
			}
		}
	}

	.correct-answer-section {
		margin-top: 16px;
		padding: 12px;
		border: 1px solid var(--el-border-color-light);
		border-radius: 6px;
		background: var(--el-fill-color-extra-light);
		display: flex;
		align-items: center;
		gap: 12px;

		.correct-answer-label {
			font-weight: 600;
			font-size: 14px;
			flex-shrink: 0;
		}

		.answer-group {
			display: flex;
			gap: 16px;
		}
	}

	.judge-options {
		.judge-display {
			margin-bottom: 16px;

			.judge-options-list {
				display: flex;
				gap: 16px;
				padding: 12px;
				border: 1px solid var(--el-border-color-light);
				border-radius: 6px;
				background: var(--el-fill-color-extra-light);

				.judge-option {
					display: flex;
					align-items: center;
					gap: 8px;

					span {
						font-size: 14px;
						color: var(--el-text-color-regular);
					}
				}
			}
		}

		.correct-answer-section {
			padding: 12px;
			border: 1px solid var(--el-border-color-light);
			border-radius: 6px;
			background: var(--el-fill-color-extra-light);
			display: flex;
			align-items: center;
			gap: 12px;

			.correct-answer-label {
				font-weight: 600;
				font-size: 14px;
				flex-shrink: 0;
			}

			.answer-group {
				display: flex;
				gap: 16px;
			}
		}
	}


}
</style>
