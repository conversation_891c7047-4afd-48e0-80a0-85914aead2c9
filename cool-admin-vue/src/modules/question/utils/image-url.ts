/**
 * 题目管理模块 - 图片URL处理工具
 * 模块内部处理，不修改框架配置
 */

/**
 * 格式化图片URL，处理开发环境和生产环境的差异
 * @param imageUrl 原始图片URL
 * @returns 格式化后的图片URL
 */
export function formatImageUrl(imageUrl: string): string {
  if (!imageUrl) return '';

  // 如果已经是完整URL，直接返回
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl;
  }

  // 开发环境：通过/dev代理访问后端静态文件
  if (import.meta.env.DEV) {
    // 如果是相对路径，添加/dev前缀
    if (imageUrl.startsWith('/upload/')) {
      return `/dev${imageUrl}`;
    }
    
    // 如果已经有/dev前缀，直接返回
    if (imageUrl.startsWith('/dev/')) {
      return imageUrl;
    }
  }

  // 生产环境或其他情况：直接返回原路径
  return imageUrl;
}

/**
 * 批量格式化图片URL数组
 * @param imageUrls 图片URL数组
 * @returns 格式化后的图片URL数组
 */
export function formatImageUrls(imageUrls: string[]): string[] {
  return imageUrls.map(url => formatImageUrl(url));
}

/**
 * 从题目内容中提取并格式化图片URL
 * @param content 题目内容HTML
 * @returns 格式化后的题目内容
 */
export function formatContentImages(content: string): string {
  if (!content) return '';

  // 匹配 <img src="..." /> 标签
  return content.replace(
    /<img([^>]*)\ssrc\s*=\s*["']([^"']+)["']([^>]*)>/gi,
    (match, before, src, after) => {
      const formattedSrc = formatImageUrl(src);
      return `<img${before} src="${formattedSrc}"${after}>`;
    }
  );
}
